"""
Enhanced content validation for Langgraph workflows.
This module provides specialized validation functions to prevent fake trading signals
and misleading financial content in Langgraph workflows.
"""

import re
from typing import Dict, Any, List, Optional
from utils.logger import Logger
from helpers.validation_helpers import detect_fake_content, validate_trading_signal_content, detect_roleplay_content

# Initialize logger
content_validator_logger = Logger("langgraph_content_validator")

class LanggraphContentValidator:
    """Enhanced content validator for Langgraph workflows"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the content validator.
        
        Args:
            config: Configuration dictionary with validation thresholds
        """
        self.config = config or {}
        self.logger = content_validator_logger
        
        # Get validation thresholds from config
        validation_config = self.config.get("application", {}).get("validation_thresholds", {})
        self.fake_content_threshold = validation_config.get("fake_content_confidence", 0.7)
        self.roleplay_threshold = validation_config.get("roleplay_confidence", 0.7)
        self.trading_signal_threshold = validation_config.get("trading_signal_confidence", 0.7)
        
    def validate_message_batch(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a batch of messages for fake content and trading signals.
        
        Args:
            messages: List of messages to validate
            
        Returns:
            Dict[str, Any]: Validation results with filtered messages
        """
        try:
            validated_messages = []
            validation_warnings = []
            
            for i, message in enumerate(messages):
                message_content = message.get("content", "") if isinstance(message, dict) else str(message)
                
                if not message_content.strip():
                    continue
                
                # Perform comprehensive validation
                validation_result = self._validate_single_message(message_content, i)
                
                if validation_result["is_valid"]:
                    validated_messages.append(message)
                else:
                    validation_warnings.extend(validation_result["warnings"])
                    self.logger.warning(f"Message {i} failed validation: {validation_result['reasons']}")
            
            return {
                "validated_messages": validated_messages,
                "original_count": len(messages),
                "validated_count": len(validated_messages),
                "filtered_count": len(messages) - len(validated_messages),
                "warnings": validation_warnings,
                "validation_passed": len(validated_messages) > 0
            }
            
        except Exception as e:
            self.logger.error(f"Error in batch validation: {str(e)}")
            return {
                "validated_messages": messages,  # Return original on error
                "original_count": len(messages),
                "validated_count": len(messages),
                "filtered_count": 0,
                "warnings": [f"Validation error: {str(e)}"],
                "validation_passed": True
            }
    
    def _validate_single_message(self, content: str, message_index: int = 0) -> Dict[str, Any]:
        """
        Validate a single message for various content issues.
        
        Args:
            content: Message content to validate
            message_index: Index of the message for logging
            
        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            warnings = []
            reasons = []
            is_valid = True
            
            # Check for fake content
            fake_detection = detect_fake_content(content, self.config)
            if fake_detection["is_fake"] and fake_detection["confidence"] > self.fake_content_threshold:
                is_valid = False
                reasons.extend(fake_detection["reasons"])
                warnings.append({
                    "type": "fake_content",
                    "message_index": message_index,
                    "confidence": fake_detection["confidence"],
                    "reasons": fake_detection["reasons"]
                })
            
            # Check for roleplay/fake trading signals
            roleplay_detection = detect_roleplay_content(content, self.config)
            if roleplay_detection["is_roleplay"] and roleplay_detection["confidence"] > self.roleplay_threshold:
                is_valid = False
                reasons.extend(roleplay_detection["reasons"])
                warnings.append({
                    "type": "roleplay_content",
                    "message_index": message_index,
                    "confidence": roleplay_detection["confidence"],
                    "reasons": roleplay_detection["reasons"]
                })
            
            # Check trading signal validity for crypto-related content
            if self._is_crypto_related(content):
                signal_validation = validate_trading_signal_content(content, self.config)
                if not signal_validation["is_valid"] and signal_validation["confidence"] > self.trading_signal_threshold:
                    is_valid = False
                    reasons.extend(signal_validation["reasons"])
                    warnings.append({
                        "type": "invalid_trading_signal",
                        "message_index": message_index,
                        "confidence": signal_validation["confidence"],
                        "reasons": signal_validation["reasons"]
                    })
            
            # Additional checks for extremely suspicious patterns
            suspicious_patterns = self._check_suspicious_patterns(content)
            if suspicious_patterns["is_suspicious"]:
                is_valid = False
                reasons.extend(suspicious_patterns["reasons"])
                warnings.append({
                    "type": "suspicious_patterns",
                    "message_index": message_index,
                    "confidence": suspicious_patterns["confidence"],
                    "reasons": suspicious_patterns["reasons"]
                })
            
            return {
                "is_valid": is_valid,
                "reasons": reasons,
                "warnings": warnings,
                "content_length": len(content),
                "validation_score": self._calculate_validation_score(fake_detection, roleplay_detection, signal_validation if self._is_crypto_related(content) else None)
            }
            
        except Exception as e:
            self.logger.error(f"Error validating message {message_index}: {str(e)}")
            return {
                "is_valid": True,  # Default to valid on error
                "reasons": [],
                "warnings": [{"type": "validation_error", "message": str(e)}],
                "content_length": len(content),
                "validation_score": 0.5
            }
    
    def _is_crypto_related(self, content: str) -> bool:
        """Check if content is crypto-related."""
        crypto_keywords = [
            r'\b(bitcoin|btc|ethereum|eth|crypto|blockchain|trading|signal)\b',
            r'\b(buy|sell|hold|long|short|target|stop.?loss)\b',
            r'\b(bullish|bearish|pump|dump|moon|lambo)\b'
        ]
        
        content_lower = content.lower()
        return any(re.search(pattern, content_lower, re.IGNORECASE) for pattern in crypto_keywords)
    
    def _check_suspicious_patterns(self, content: str) -> Dict[str, Any]:
        """Check for additional suspicious patterns specific to fake trading signals."""
        suspicious_patterns = [
            # Fake precision patterns
            (r'\b(?:Target Price|Price Target):\s*\$?\d+,?\d*\.\d{2,}\b', 0.8, "fake_precision_pricing"),
            # Unrealistic timeframes
            (r'\bDuration:\s*(?:1\s*Hour?\s*\d+\s*Minutes?|70\s*min|1hr\s*10min)\b', 0.9, "suspicious_duration_format"),
            # Generic trading advice patterns
            (r'\b(?:Remember|Don\'t forget).*(?:risk management|market conditions)\b', 0.7, "generic_trading_advice"),
            # Fake urgency with specific numbers
            (r'\b(?:Target|Stop)\s*(?:Price|Loss):\s*\$\d+,\d{3}\b', 0.8, "fake_specific_targets"),
            # Role-playing language
            (r'\b(?:Alright|Good evening|Hello everyone),?\s*(?:folks|traders|everyone)\b', 0.8, "roleplay_greeting"),
        ]
        
        reasons = []
        max_confidence = 0.0
        
        content_lower = content.lower()
        for pattern, confidence, reason in suspicious_patterns:
            if re.search(pattern, content, re.IGNORECASE | re.MULTILINE):
                reasons.append(reason)
                max_confidence = max(max_confidence, confidence)
        
        return {
            "is_suspicious": len(reasons) > 0 and max_confidence > 0.7,
            "reasons": reasons,
            "confidence": max_confidence
        }
    
    def _calculate_validation_score(self, fake_detection: Dict, roleplay_detection: Dict, signal_validation: Optional[Dict] = None) -> float:
        """Calculate an overall validation score (0.0 = invalid, 1.0 = valid)."""
        try:
            score = 1.0
            
            # Reduce score based on fake content confidence
            if fake_detection["is_fake"]:
                score -= fake_detection["confidence"] * 0.4
            
            # Reduce score based on roleplay confidence
            if roleplay_detection["is_roleplay"]:
                score -= roleplay_detection["confidence"] * 0.4
            
            # Reduce score based on trading signal validation
            if signal_validation and not signal_validation["is_valid"]:
                score -= signal_validation["confidence"] * 0.3
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            self.logger.error(f"Error calculating validation score: {str(e)}")
            return 0.5  # Default neutral score
    
    def validate_generated_content(self, content: str, content_type: str = "general") -> Dict[str, Any]:
        """
        Validate generated content to ensure it doesn't contain fake trading signals.
        
        Args:
            content: Generated content to validate
            content_type: Type of content (article, social_post, etc.)
            
        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            validation_result = self._validate_single_message(content)
            
            # Additional checks for generated content
            if content_type in ["social_post", "trading_signal", "crypto_analysis"]:
                # Stricter validation for trading-related content
                if validation_result["validation_score"] < 0.8:
                    validation_result["is_valid"] = False
                    validation_result["reasons"].append("generated_content_quality_threshold")
            
            # Check for placeholder content
            placeholder_indicators = [
                "placeholder", "template", "example", "sample",
                "lorem ipsum", "xxx", "todo", "tbd"
            ]
            
            content_lower = content.lower()
            if any(indicator in content_lower for indicator in placeholder_indicators):
                validation_result["is_valid"] = False
                validation_result["reasons"].append("placeholder_content_detected")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Error validating generated content: {str(e)}")
            return {
                "is_valid": False,
                "reasons": [f"validation_error: {str(e)}"],
                "warnings": [],
                "content_length": len(content),
                "validation_score": 0.0
            }
