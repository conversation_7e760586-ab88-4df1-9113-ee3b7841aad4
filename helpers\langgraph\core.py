"""
Langgraph Core Module

This module provides core functionality for building stateful,
multi-step AI workflows using Langgraph.
"""

from typing import Annotated, Sequence
import operator
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable
from langgraph.graph import StateGraph, END
from utils.logger import Logger
import logging

# Initialize logger
langgraph_logger = Logger("langgraph_core")

@dataclass
class GraphState:
    """State for the Langgraph workflow"""
    messages: Annotated[Sequence[Any], operator.add] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    response: Optional[str] = None
    next_action: Optional[str] = None
    is_complete: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    # Crypto-specific fields
    crypto_signals: List[Dict[str, Any]] = field(default_factory=list)
    market_analysis: Optional[Dict[str, Any]] = None
    unique_content: Optional[str] = None
    # Content generation fields
    content_topic: Optional[str] = None
    content_type: Optional[str] = None
    content_style: Optional[Dict[str, Any]] = None
    generated_content: Optional[str] = None

class LanggraphWorkflow:
    """Base class for Langgraph workflows"""
    
    def __init__(self, name: str = "default_workflow"):
        """
        Initialize the Langgraph workflow.
        
        Args:
            name: Name of the workflow
        """
        self.name = name
        self.graph = StateGraph(GraphState)
        self.workflow = None
        self.logger = langgraph_logger
        
    def add_node(self, name: str, action: Callable):
        """
        Add a node to the workflow graph.
        
        Args:
            name: Name of the node
            action: Function to execute for this node
        """
        try:
            self.graph.add_node(name, action)
            self.logger.debug("Added node to workflow", node_name=name, workflow_name=self.name)
        except Exception as e:
            self.logger.error("Error adding node to workflow", node_name=name, workflow_name=self.name, error=str(e))
            raise
            
    def add_edge(self, start_key: str, end_key: str):
        """
        Add an edge between two nodes in the workflow graph.
        
        Args:
            start_key: Starting node
            end_key: Ending node
        """
        try:
            self.graph.add_edge(start_key, end_key)
            self.logger.debug("Added edge in workflow", start_key=start_key, end_key=end_key, workflow_name=self.name)
        except Exception as e:
            self.logger.error("Error adding edge in workflow", start_key=start_key, end_key=end_key, workflow_name=self.name, error=str(e))
            raise
            
    def set_entry_point(self, node_name: str):
        """
        Set the entry point for the workflow.
        
        Args:
            node_name: Name of the entry point node
        """
        try:
            self.graph.set_entry_point(node_name)
            self.logger.debug("Set entry point for workflow", node_name=node_name, workflow_name=self.name)
        except Exception as e:
            self.logger.error("Error setting entry point for workflow", node_name=node_name, workflow_name=self.name, error=str(e))
            raise
            
    def set_finish_point(self, node_name: str):
        """
        Set the finish point for the workflow.
        
        Args:
            node_name: Name of the finish point node
        """
        try:
            self.graph.add_edge(node_name, END)
            self.logger.debug("Set finish point for workflow", node_name=node_name, workflow_name=self.name)
        except Exception as e:
            self.logger.error("Error setting finish point for workflow", node_name=node_name, workflow_name=self.name, error=str(e))
            raise
            
    def compile(self):
        """Compile the workflow graph."""
        try:
            self.workflow = self.graph.compile()
            self.logger.info("Compiled workflow successfully", workflow_name=self.name)
        except Exception as e:
            self.logger.error("Error compiling workflow", workflow_name=self.name, error=str(e))
            raise
            
    async def invoke(self, input_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Invoke the workflow with the given input state.
        
        Args:
            input_state: Initial state for the workflow
            
        Returns:
            Dict[str, Any]: Final state after workflow execution
        """
        try:
            if not self.workflow:
                self.compile()
                
            result = await self.workflow.ainvoke(input_state)
            self.logger.info("Workflow executed successfully", workflow_name=self.name)
            return result
            
        except Exception as e:
            self.logger.error("Error executing workflow", workflow_name=self.name, error=str(e))
            raise

class MessageProcessingWorkflow(LanggraphWorkflow):
    """Specialized workflow for processing Telegram messages"""
    
    def __init__(self, name: str = "message_processing_workflow"):
        """
        Initialize the message processing workflow.
        
        Args:
            name: Name of the workflow
        """
        super().__init__(name)
        self._setup_default_workflow()
        
    def _setup_default_workflow(self):
        """Set up the default message processing workflow."""
        try:
            # Add nodes
            self.add_node("preprocess", self._preprocess_message)
            self.add_node("analyze", self._analyze_message)
            self.add_node("postprocess", self._postprocess_response)
            
            # Set up edges
            self.set_entry_point("preprocess")
            self.add_edge("preprocess", "analyze")
            self.add_edge("analyze", "postprocess")
            self.set_finish_point("postprocess")
            
            self.logger.info("Set up default workflow", workflow_name=self.name)
            
        except Exception as e:
            self.logger.error("Error setting up default workflow", workflow_name=self.name, error=str(e))
            raise
            
    async def _preprocess_message(self, state: GraphState) -> Dict[str, Any]:
        """
        Preprocess the message.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state
        """
        try:
            messages = state.messages
            context = state.context
            
            # Add preprocessing logic here
            self.logger.debug("Preprocessing message")
            
            # Check for message_thread_id in context
            if 'message_thread_id' in context:
                self.logger.debug("Processing message for thread ID", message_thread_id=context['message_thread_id'])
            
            return {
                "messages": messages,
                "context": context,
                "next_action": "analyze"
            }
            
        except Exception as e:
            self.logger.error(f"Error in preprocessing: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "next_action": "end",
                "is_complete": True
            }
            
    async def _analyze_message(self, state: GraphState) -> Dict[str, Any]:
        """
        Analyze the message using LLM with enhanced fake content detection.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state
        """
        try:
            messages = state.messages
            context = state.context

            # Add analysis logic here
            self.logger.debug("Analyzing message")

            # Enhanced fake content detection for each message
            from helpers.validation_helpers import detect_fake_content, validate_trading_signal_content, detect_roleplay_content

            for message in messages:
                message_content = message.get("content", "") if isinstance(message, dict) else str(message)

                # Detect fake content
                fake_detection = detect_fake_content(message_content)
                if fake_detection["is_fake"] and fake_detection["confidence"] > 0.7:
                    self.logger.warning(f"Fake content detected in message: {fake_detection['reasons']}")
                    context["content_warnings"] = context.get("content_warnings", [])
                    context["content_warnings"].append({
                        "type": "fake_content",
                        "confidence": fake_detection["confidence"],
                        "reasons": fake_detection["reasons"]
                    })

                # Detect roleplay content (fake trading signals)
                roleplay_detection = detect_roleplay_content(message_content)
                if roleplay_detection["is_roleplay"] and roleplay_detection["confidence"] > 0.7:
                    self.logger.warning(f"Roleplay/fake trading content detected: {roleplay_detection['reasons']}")
                    context["content_warnings"] = context.get("content_warnings", [])
                    context["content_warnings"].append({
                        "type": "roleplay_content",
                        "confidence": roleplay_detection["confidence"],
                        "reasons": roleplay_detection["reasons"]
                    })

                # Validate trading signals if crypto-related
                if any(crypto_term in message_content.lower() for crypto_term in ['btc', 'bitcoin', 'eth', 'ethereum', 'crypto', 'trading', 'signal']):
                    signal_validation = validate_trading_signal_content(message_content)
                    if not signal_validation["is_valid"] and signal_validation["confidence"] > 0.7:
                        self.logger.warning(f"Invalid trading signal detected: {signal_validation['reasons']}")
                        context["content_warnings"] = context.get("content_warnings", [])
                        context["content_warnings"].append({
                            "type": "invalid_trading_signal",
                            "confidence": signal_validation["confidence"],
                            "reasons": signal_validation["reasons"]
                        })

            # Include message_thread_id in analysis context if present
            if 'message_thread_id' in context:
                self.logger.debug(f"Analyzing message for thread ID: {context['message_thread_id']}")

            return {
                "messages": messages,
                "context": context,
                "next_action": "postprocess"
            }

        except Exception as e:
            self.logger.error(f"Error in analysis: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "next_action": "end",
                "is_complete": True
            }
            
    async def _postprocess_response(self, state: GraphState) -> Dict[str, Any]:
        """
        Postprocess the response.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state
        """
        try:
            messages = state.messages
            context = state.context
            
            # Add postprocessing logic here
            self.logger.debug("Postprocessing response")
            
            # Handle message_thread_id in response if present
            response_parts = ["Processed response"]
            
            if 'message_thread_id' in context and context['message_thread_id'] is not None:
                response_parts.append(f"for thread {context['message_thread_id']}")
            
            if 'date_context' in context:
                response_parts.append(f"({context['date_context']})")
            elif 'processing_timestamp' in context:
                response_parts.append(f"(processed on {context['processing_timestamp']})")
                
            if 'is_reply' in context and context['is_reply']:
                if 'processing_original_message' in context and context['processing_original_message']:
                    response_parts.append("(processed original message instead of brief reply)")
                else:
                    response_parts.append("(this was a reply message)")
                
            response = " ".join(response_parts)
            
            return {
                "messages": messages,
                "context": context,
                "response": response,
                "is_complete": True
            }
            
        except Exception as e:
            self.logger.error(f"Error in postprocessing: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "response": "Error in processing",
                "is_complete": True
            }

class TelegramMessageProcessingWorkflow(MessageProcessingWorkflow):
    """Specialized workflow for processing Telegram messages with thread support and date filtering"""
    
    def __init__(self, name: str = "telegram_message_processing_workflow"):
        """
        Initialize the Telegram message processing workflow.
        
        Args:
            name: Name of the workflow
        """
        super().__init__(name)
        
    async def _preprocess_message(self, state: GraphState) -> Dict[str, Any]:
        """
        Preprocess the Telegram message, including thread-specific processing and date filtering.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state
        """
        try:
            messages = state.messages
            context = state.context
            
            # Add preprocessing logic here
            self.logger.debug("Preprocessing Telegram message")
            
            # Handle message_thread_id specifically for Telegram
            if 'message_thread_id' in context:
                thread_id = context['message_thread_id']
                self.logger.debug(f"Processing Telegram message for thread ID: {thread_id}")
                # Add thread-specific preprocessing logic here if needed
                
            # Handle date filtering if specified in context
            if 'date_range' in context:
                date_range = context['date_range']
                self.logger.debug(f"Filtering messages by date range: {date_range}")
                # Add date filtering logic here if needed
                # For now, we'll just log that we're aware of the date range
                start_date = date_range.get('start')
                end_date = date_range.get('end')
                if start_date:
                    self.logger.debug(f"Date range start: {start_date}")
                if end_date:
                    self.logger.debug(f"Date range end: {end_date}")
                
            return {
                "messages": messages,
                "context": context,
                "next_action": "analyze"
            }
            
        except Exception as e:
            self.logger.error(f"Error in Telegram message preprocessing: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "next_action": "end",
                "is_complete": True
            }
            
    async def _analyze_message(self, state: GraphState) -> Dict[str, Any]:
        """
        Analyze the Telegram message using LLM with thread context and date information.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state
        """
        try:
            messages = state.messages
            context = state.context
            
            # Add analysis logic here
            self.logger.debug("Analyzing Telegram message")
            
            # Include message_thread_id in analysis context if present
            analysis_context = context.copy()
            if 'message_thread_id' in context:
                thread_id = context['message_thread_id']
                self.logger.debug(f"Analyzing Telegram message for thread ID: {thread_id}")
                analysis_context['thread_context'] = f"This message is from thread {thread_id}"
                
            # Include date information in analysis context if present
            if 'date_context' in context:
                date_context = context['date_context']
                self.logger.debug(f"Analyzing Telegram message with date context: {date_context}")
                analysis_context['date_context'] = date_context
            elif 'date_range' in context:
                date_range = context['date_range']
                self.logger.debug(f"Analyzing Telegram message with date range: {date_range}")
                start_date = date_range.get('start')
                end_date = date_range.get('end')
                if start_date and end_date:
                    analysis_context['date_context'] = f"This analysis covers messages from {start_date} to {end_date}"
                elif start_date:
                    analysis_context['date_context'] = f"This analysis covers messages from {start_date} onwards"
                elif end_date:
                    analysis_context['date_context'] = f"This analysis covers messages up to {end_date}"
                else:
                    analysis_context['date_context'] = "This analysis covers historical messages"
            elif 'processing_timestamp' in context:
                # Include processing timestamp if date_range is not specified
                timestamp = context['processing_timestamp']
                analysis_context['date_context'] = f"This analysis was performed on {timestamp}"
                
            # Include reply context if available
            if 'is_reply' in context and context['is_reply']:
                if 'reply_context' in context:
                    analysis_context['reply_context'] = context['reply_context']
                if 'original_message' in context and context['original_message']:
                    analysis_context['original_message'] = context['original_message']
                # Include information about which message is being processed
                if 'processing_original_message' in context and context['processing_original_message']:
                    analysis_context['processing_note'] = "Processing original message instead of brief reply"
                elif 'processed_message_type' in context:
                    analysis_context['processed_message_type'] = context['processed_message_type']
                # Add information about combined processing
                if 'message_content' in context and context['message_content'].startswith("Reply: ") and "\n\nOriginal Message: " in context['message_content']:
                    analysis_context['combined_reply_processing'] = True
                
            return {
                "messages": messages,
                "context": analysis_context,
                "next_action": "postprocess"
            }
            
        except Exception as e:
            self.logger.error(f"Error in Telegram message analysis: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "next_action": "end",
                "is_complete": True
            }

class CryptoAnalysisWorkflow(LanggraphWorkflow):
    """Specialized workflow for crypto signal analysis and unique content generation"""
    
    def __init__(self, name: str = "crypto_analysis_workflow"):
        """
        Initialize the crypto analysis workflow.
        
        Args:
            name: Name of the workflow
        """
        super().__init__(name)
        self._setup_crypto_workflow()
        
    def _setup_crypto_workflow(self):
        """Set up the crypto analysis workflow."""
        try:
            # Add nodes
            self.add_node("extract_signals", self._extract_crypto_signals)
            self.add_node("analyze_market", self._analyze_market_conditions)
            self.add_node("generate_content", self._generate_unique_content)
            self.add_node("validate_output", self._validate_output)
            
            # Set up edges
            self.set_entry_point("extract_signals")
            self.add_edge("extract_signals", "analyze_market")
            self.add_edge("analyze_market", "generate_content")
            self.add_edge("generate_content", "validate_output")
            self.set_finish_point("validate_output")
            
            self.logger.info("Set up crypto analysis workflow", workflow_name=self.name)
            
        except Exception as e:
            self.logger.error(f"Error setting up crypto analysis workflow for '{self.name}': {str(e)}")
            raise
            
    async def _extract_crypto_signals(self, state: GraphState) -> Dict[str, Any]:
        """
        Extract crypto signals from messages with enhanced fake content filtering.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with extracted signals
        """
        try:
            messages = state.messages
            context = state.context

            self.logger.debug("Extracting crypto signals from messages")

            # Import validation helpers
            from helpers.validation_helpers import detect_fake_content, validate_trading_signal_content, detect_roleplay_content

            # Extract crypto signals logic with enhanced validation
            crypto_signals = []
            filtered_messages = []

            for message in messages:
                message_content = message.get("content", "") if isinstance(message, dict) else str(message)

                # Skip empty messages
                if not message_content.strip():
                    continue

                # Validate content before processing
                fake_detection = detect_fake_content(message_content)
                roleplay_detection = detect_roleplay_content(message_content)

                # Skip messages with high fake content confidence
                if fake_detection["is_fake"] and fake_detection["confidence"] > 0.8:
                    self.logger.warning(f"Skipping fake content: {fake_detection['reasons']}")
                    continue

                # Skip messages with high roleplay confidence (fake trading signals)
                if roleplay_detection["is_roleplay"] and roleplay_detection["confidence"] > 0.8:
                    self.logger.warning(f"Skipping roleplay content: {roleplay_detection['reasons']}")
                    continue

                # Check for crypto-related content
                crypto_keywords = {
                    "bitcoin": "BTC", "btc": "BTC",
                    "ethereum": "ETH", "eth": "ETH",
                    "solana": "SOL", "sol": "SOL",
                    "cardano": "ADA", "ada": "ADA",
                    "polkadot": "DOT", "dot": "DOT",
                    "dogecoin": "DOGE", "doge": "DOGE"
                }

                content_lower = message_content.lower()
                for keyword, symbol in crypto_keywords.items():
                    if keyword in content_lower:
                        # Validate trading signal if present
                        signal_validation = validate_trading_signal_content(message_content)

                        # Only include signals that pass validation or have low confidence issues
                        if signal_validation["is_valid"] or signal_validation["confidence"] < 0.6:
                            crypto_signals.append({
                                "symbol": symbol,
                                "signal_type": "mention",
                                "strength": "high" if signal_validation["is_valid"] else "low",
                                "content": message_content,
                                "validation_score": signal_validation.get("structure_score", 0),
                                "confidence": 1.0 - signal_validation["confidence"]  # Invert confidence for signal strength
                            })
                            filtered_messages.append(message)
                        else:
                            self.logger.warning(f"Skipping invalid trading signal for {symbol}: {signal_validation['reasons']}")
                        break

            # Update context with filtering information
            context["filtered_message_count"] = len(filtered_messages)
            context["original_message_count"] = len(messages)
            context["signals_extracted"] = len(crypto_signals)

            return {
                "messages": filtered_messages,
                "context": context,
                "crypto_signals": crypto_signals,
                "next_action": "analyze_market"
            }

        except Exception as e:
            self.logger.error(f"Error in crypto signal extraction: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "crypto_signals": [],
                "next_action": "end",
                "is_complete": True
            }
            
    async def _analyze_market_conditions(self, state: GraphState) -> Dict[str, Any]:
        """
        Analyze market conditions based on extracted signals.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with market analysis
        """
        try:
            messages = state.messages
            context = state.context
            crypto_signals = state.crypto_signals
            
            self.logger.debug("Analyzing market conditions")
            
            # Market analysis logic would go here
            # This is a placeholder implementation
            market_analysis = {
                "overall_sentiment": "neutral",
                "volatility_level": "medium",
                "key_signals": len(crypto_signals),
                "analysis_timestamp": context.get("timestamp", "unknown")
            }
            
            # Determine sentiment based on signals
            if crypto_signals:
                positive_signals = sum(1 for signal in crypto_signals if signal.get("strength") == "high")
                if positive_signals > len(crypto_signals) / 2:
                    market_analysis["overall_sentiment"] = "positive"
                elif positive_signals < len(crypto_signals) / 3:
                    market_analysis["overall_sentiment"] = "negative"
            
            return {
                "messages": messages,
                "context": context,
                "crypto_signals": crypto_signals,
                "market_analysis": market_analysis,
                "next_action": "generate_content"
            }
            
        except Exception as e:
            self.logger.error(f"Error in market analysis: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "crypto_signals": state.crypto_signals,
                "market_analysis": {},
                "next_action": "end",
                "is_complete": True
            }
            
    async def _generate_unique_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Generate unique content based on market analysis.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with generated content
        """
        try:
            messages = state.messages
            context = state.context
            crypto_signals = state.crypto_signals
            market_analysis = state.market_analysis
            
            self.logger.debug("Generating unique content")
            
            # Content generation logic would go here
            # This is a placeholder implementation
            content_template = """
Crypto Market Analysis Report
============================

_signals_ crypto signals detected in recent messages.

Market Sentiment: _sentiment_
Volatility Level: _volatility_

Key Insights:
- _insight1_
- _insight2_

Generated at: _timestamp_
"""
            
            unique_content = content_template.replace("_signals_", str(len(crypto_signals)))
            unique_content = unique_content.replace("_sentiment_", market_analysis.get("overall_sentiment", "unknown"))
            unique_content = unique_content.replace("_volatility_", market_analysis.get("volatility_level", "unknown"))
            unique_content = unique_content.replace("_timestamp_", market_analysis.get("analysis_timestamp", "unknown"))
            
            # Add some basic insights
            if crypto_signals:
                insight1 = f"Most mentioned asset: {crypto_signals[0].get('symbol', 'unknown')}"
            else:
                insight1 = "No significant crypto signals detected"
                
            insight2 = f"Total signals analyzed: {len(crypto_signals)}"
            
            unique_content = unique_content.replace("_insight1_", insight1)
            unique_content = unique_content.replace("_insight2_", insight2)
            
            return {
                "messages": messages,
                "context": context,
                "crypto_signals": crypto_signals,
                "market_analysis": market_analysis,
                "unique_content": unique_content,
                "next_action": "validate_output"
            }
            
        except Exception as e:
            self.logger.error(f"Error in content generation: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "crypto_signals": state.crypto_signals,
                "market_analysis": state.market_analysis,
                "unique_content": "Error generating content",
                "next_action": "end",
                "is_complete": True
            }
            
    async def _validate_output(self, state: GraphState) -> Dict[str, Any]:
        """
        Validate the generated output.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with validation result
        """
        try:
            messages = state.messages
            context = state.context
            crypto_signals = state.crypto_signals
            market_analysis = state.market_analysis
            unique_content = state.unique_content
            
            self.logger.debug("Validating output")
            
            # Validation logic would go here
            # This is a placeholder implementation
            is_valid = unique_content is not None and len(unique_content.strip()) > 0
            
            return {
                "messages": messages,
                "context": context,
                "crypto_signals": crypto_signals,
                "market_analysis": market_analysis,
                "unique_content": unique_content if is_valid else "Validation failed: Content generation error",
                "is_complete": True
            }
            
        except Exception as e:
            self.logger.error(f"Error in output validation: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "crypto_signals": state.crypto_signals,
                "market_analysis": state.market_analysis,
                "unique_content": "Validation failed due to error",
                "is_complete": True
            }

class ContentGenerationWorkflow(LanggraphWorkflow):
    """Specialized workflow for unique content generation"""

    def __init__(self, name: str = "content_generation_workflow"):
        """
        Initialize the content generation workflow.

        Args:
            name: Name of the workflow
        """
        super().__init__(name)
        self._setup_content_workflow()

    def _setup_content_workflow(self):
        """Set up the content generation workflow."""
        try:
            # Add nodes
            self.add_node("define_topic", self._define_content_topic)
            self.add_node("select_style", self._select_content_style)
            self.add_node("generate_content", self._generate_content)
            self.add_node("review_content", self._review_content)

            # Set up edges
            self.set_entry_point("define_topic")
            self.add_edge("define_topic", "select_style")
            self.add_edge("select_style", "generate_content")
            self.add_edge("generate_content", "review_content")
            self.set_finish_point("review_content")

            self.logger.info("Set up content generation workflow", workflow_name=self.name)

        except Exception as e:
            self.logger.error(f"Error setting up content generation workflow for '{self.name}': {str(e)}")
            raise


class MasterContentWorkflow(LanggraphWorkflow):
    """
    Master workflow that serves as the single entry point for all content processing.
    Automatically detects content type and routes to appropriate specialized pipelines.
    """

    def __init__(self, name: str = "master_content_workflow"):
        """
        Initialize the master content workflow.

        Args:
            name: Name of the workflow
        """
        super().__init__(name)

        # Load configuration for routing thresholds
        from utils.config_loader import ConfigLoader
        self.config = ConfigLoader.load_config()
        self.master_config = self.config.get('master_workflow', {})

        # Set routing thresholds from config
        self.crypto_detection_threshold = self.master_config.get('crypto_detection_threshold', 0.6)
        self.crypto_ratio_threshold = self.master_config.get('crypto_ratio_threshold', 0.8)
        self.mixed_content_threshold = self.master_config.get('mixed_content_threshold', 0.2)
        self.routing_preferences = self.master_config.get('routing_preferences', {})

        self._setup_master_workflow()

    def _setup_master_workflow(self):
        """Set up the master content workflow with routing logic."""
        try:
            # Add nodes for the master pipeline
            self.add_node("classify_content", self._classify_content)
            self.add_node("route_content", self._route_content)
            self.add_node("process_crypto", self._process_crypto_content)
            self.add_node("process_normal", self._process_normal_content)
            self.add_node("process_mixed", self._process_mixed_content)
            self.add_node("harmonize_output", self._harmonize_output)

            # Set up edges
            self.set_entry_point("classify_content")
            self.add_edge("classify_content", "route_content")

            # Conditional routing will be handled in _route_content
            self.add_edge("route_content", "process_crypto")
            self.add_edge("route_content", "process_normal")
            self.add_edge("route_content", "process_mixed")

            # All processing paths lead to output harmonization
            self.add_edge("process_crypto", "harmonize_output")
            self.add_edge("process_normal", "harmonize_output")
            self.add_edge("process_mixed", "harmonize_output")

            self.set_finish_point("harmonize_output")

            self.logger.info("Set up master content workflow", workflow_name=self.name)

        except Exception as e:
            self.logger.error(f"Error setting up master content workflow for '{self.name}': {str(e)}")
            raise

    async def _classify_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Classify content to determine if it's crypto-related, normal, or mixed.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with content classification
        """
        try:
            messages = state.messages
            context = state.context

            self.logger.debug("Classifying content type for routing")

            # Import content classification helpers
            from helpers.validation_helpers import detect_fake_content
            from helpers.langgraph.content_validator import LanggraphContentValidator
            from utils.config_loader import ConfigLoader

            # Initialize content validator for crypto detection
            config = ConfigLoader.load_config()
            content_validator = LanggraphContentValidator(config)

            # Analyze each message for content type
            crypto_count = 0
            normal_count = 0
            total_messages = len(messages)

            crypto_confidence_scores = []
            message_classifications = []

            for i, message in enumerate(messages):
                message_content = message.get('content', '') if isinstance(message, dict) else str(message)

                # Use existing crypto detection logic
                is_crypto = content_validator._is_crypto_related(message_content)

                # Enhanced crypto detection with confidence scoring
                crypto_confidence = self._calculate_crypto_confidence(message_content)
                crypto_confidence_scores.append(crypto_confidence)

                classification = {
                    "message_index": i,
                    "is_crypto": is_crypto,
                    "crypto_confidence": crypto_confidence,
                    "content_length": len(message_content)
                }
                message_classifications.append(classification)

                if is_crypto or crypto_confidence > self.crypto_detection_threshold:
                    crypto_count += 1
                else:
                    normal_count += 1

            # Determine overall content type
            crypto_ratio = crypto_count / total_messages if total_messages > 0 else 0
            avg_crypto_confidence = sum(crypto_confidence_scores) / len(crypto_confidence_scores) if crypto_confidence_scores else 0

            # Classification logic using configurable thresholds
            strict_classification = self.routing_preferences.get('strict_crypto_classification', False)
            prefer_crypto = self.routing_preferences.get('prefer_crypto_pipeline', False)
            enable_mixed = self.routing_preferences.get('enable_mixed_processing', True)

            if strict_classification:
                # Stricter classification - higher thresholds
                crypto_threshold = self.crypto_ratio_threshold * 1.2
                mixed_threshold = self.mixed_content_threshold * 0.5
            else:
                crypto_threshold = self.crypto_ratio_threshold
                mixed_threshold = self.mixed_content_threshold

            if crypto_ratio >= crypto_threshold or avg_crypto_confidence >= crypto_threshold:
                content_type = "crypto"
            elif crypto_ratio <= mixed_threshold and avg_crypto_confidence <= (mixed_threshold + 0.1):
                content_type = "normal"
            elif enable_mixed:
                content_type = "mixed"
            else:
                # If mixed processing is disabled, route to preferred pipeline
                content_type = "crypto" if prefer_crypto else "normal"

            self.logger.info(f"Content classified as: {content_type}",
                           crypto_ratio=crypto_ratio,
                           avg_confidence=avg_crypto_confidence,
                           total_messages=total_messages)

            return {
                "messages": messages,
                "context": context,
                "content_type": content_type,
                "crypto_ratio": crypto_ratio,
                "crypto_confidence": avg_crypto_confidence,
                "message_classifications": message_classifications,
                "next_action": "route_content"
            }

        except Exception as e:
            self.logger.error(f"Error in content classification: {str(e)}")
            return {
                "messages": state.messages,
                "context": state.context,
                "content_type": "normal",  # Default to normal on error
                "crypto_ratio": 0.0,
                "crypto_confidence": 0.0,
                "message_classifications": [],
                "next_action": "route_content"
            }

    def _calculate_crypto_confidence(self, content: str) -> float:
        """
        Calculate confidence score for crypto-related content.

        Args:
            content: Message content to analyze

        Returns:
            float: Confidence score between 0.0 and 1.0
        """
        try:
            import re

            # Enhanced crypto keyword patterns with weights
            crypto_patterns = {
                # High confidence patterns (0.8-1.0)
                r'\b(bitcoin|btc|ethereum|eth|crypto|blockchain)\b': 0.9,
                r'\b(buy|sell|hold|long|short|target|stop.?loss)\b': 0.8,
                r'\b(trading|signal|analysis|chart|technical)\b': 0.8,

                # Medium confidence patterns (0.5-0.7)
                r'\b(bullish|bearish|pump|dump|moon|lambo)\b': 0.6,
                r'\b(solana|sol|cardano|ada|polkadot|dot|dogecoin|doge)\b': 0.7,
                r'\b(defi|nft|nfts|web3|token|coin|altcoin)\b': 0.6,

                # Lower confidence patterns (0.3-0.5)
                r'\b(market|price|value|investment|portfolio)\b': 0.3,
                r'\b(profit|loss|gain|return|roi)\b': 0.4,
                r'\$[0-9]+': 0.3,  # Price mentions
            }

            content_lower = content.lower()
            confidence_scores = []

            for pattern, weight in crypto_patterns.items():
                matches = re.findall(pattern, content_lower, re.IGNORECASE)
                if matches:
                    # Multiple matches increase confidence
                    match_confidence = min(weight * (1 + len(matches) * 0.1), 1.0)
                    confidence_scores.append(match_confidence)

            if not confidence_scores:
                return 0.0

            # Calculate weighted average with diminishing returns
            final_confidence = min(sum(confidence_scores) / len(confidence_scores), 1.0)

            # Boost confidence if multiple different patterns match
            if len(confidence_scores) > 3:
                final_confidence = min(final_confidence * 1.2, 1.0)

            return final_confidence

        except Exception as e:
            self.logger.error(f"Error calculating crypto confidence: {str(e)}")
            return 0.0

    async def _route_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Route content to appropriate processing pipeline based on classification.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with routing decision
        """
        try:
            fallback_pipeline = self.master_config.get('fallback_pipeline', 'normal')
            content_type = getattr(state, 'content_type', None) or state.get('content_type', 'normal') if hasattr(state, 'get') else 'normal'

            self.logger.debug(f"Routing content to {content_type} pipeline")

            # Set next action based on content type with fallback logic
            if content_type == "crypto":
                next_action = "process_crypto"
            elif content_type == "mixed":
                # Check if mixed processing is enabled
                if self.routing_preferences.get('enable_mixed_processing', True):
                    next_action = "process_mixed"
                else:
                    # Fall back to preferred pipeline
                    if self.routing_preferences.get('prefer_crypto_pipeline', False):
                        next_action = "process_crypto"
                        content_type = "crypto"
                    else:
                        next_action = "process_normal"
                        content_type = "normal"
            else:
                next_action = "process_normal"

            # Log routing decision if enabled
            if self.master_config.get('enable_pipeline_logging', True):
                crypto_ratio = getattr(state, 'crypto_ratio', None) or (state.get('crypto_ratio', 0) if hasattr(state, 'get') else 0)
                crypto_confidence = getattr(state, 'crypto_confidence', None) or (state.get('crypto_confidence', 0) if hasattr(state, 'get') else 0)
                self.logger.info(f"Content routed to {content_type} pipeline",
                               crypto_ratio=crypto_ratio,
                               crypto_confidence=crypto_confidence)

            # Create return state dict
            return_state = {
                "routing_decision": content_type,
                "next_action": next_action,
                "fallback_used": content_type != (getattr(state, 'content_type', None) or 'normal')
            }

            # Add existing state attributes
            if hasattr(state, '__dict__'):
                return_state.update(state.__dict__)
            elif hasattr(state, 'get'):
                return_state.update(state)

            return return_state

        except Exception as e:
            self.logger.error(f"Error in content routing: {str(e)}")
            # Use configured fallback pipeline on error
            fallback_action = f"process_{fallback_pipeline}"

            # Create return state dict for error case
            return_state = {
                "routing_decision": fallback_pipeline,
                "next_action": fallback_action,
                "routing_error": str(e),
                "fallback_used": True
            }

            # Add existing state attributes
            if hasattr(state, '__dict__'):
                return_state.update(state.__dict__)
            elif hasattr(state, 'get'):
                return_state.update(state)

            return return_state

    async def _process_crypto_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Process crypto-related content using the crypto analysis workflow.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with crypto processing results
        """
        try:
            self.logger.debug("Processing content through crypto pipeline")

            # Create and run crypto analysis workflow
            crypto_workflow = CryptoAnalysisWorkflow("crypto_processing_sub_workflow")

            # Prepare input state for crypto workflow
            crypto_input_state = {
                "messages": state.messages,
                "context": state.context
            }

            # Run crypto workflow
            crypto_result = await crypto_workflow.invoke(crypto_input_state)

            return {
                **state,
                "processing_pipeline": "crypto",
                "crypto_signals": crypto_result.get('crypto_signals', []),
                "market_analysis": crypto_result.get('market_analysis', {}),
                "unique_content": crypto_result.get('unique_content', ''),
                "processing_result": crypto_result,
                "next_action": "harmonize_output"
            }

        except Exception as e:
            self.logger.error(f"Error in crypto content processing: {str(e)}")
            return {
                **state,
                "processing_pipeline": "crypto",
                "crypto_signals": [],
                "market_analysis": {},
                "unique_content": "Error processing crypto content",
                "processing_error": str(e),
                "next_action": "harmonize_output"
            }

    async def _process_normal_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Process normal content using the standard message processing workflow.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with normal processing results
        """
        try:
            self.logger.debug("Processing content through normal pipeline")

            # Create and run message processing workflow
            normal_workflow = TelegramMessageProcessingWorkflow("normal_processing_sub_workflow")

            # Prepare input state for normal workflow
            normal_input_state = {
                "messages": state.messages,
                "context": state.context
            }

            # Run normal workflow
            normal_result = await normal_workflow.invoke(normal_input_state)

            return {
                **state,
                "processing_pipeline": "normal",
                "response": normal_result.get('response', ''),
                "analysis_result": normal_result.get('analysis_result', {}),
                "processing_result": normal_result,
                "next_action": "harmonize_output"
            }

        except Exception as e:
            self.logger.error(f"Error in normal content processing: {str(e)}")
            return {
                **state,
                "processing_pipeline": "normal",
                "response": "Error processing normal content",
                "analysis_result": {},
                "processing_error": str(e),
                "next_action": "harmonize_output"
            }

    async def _process_mixed_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Process mixed content using both crypto and normal pipelines.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with mixed processing results
        """
        try:
            self.logger.debug("Processing content through mixed pipeline")

            # Separate crypto and normal messages based on individual classifications
            message_classifications = state.get('message_classifications', [])
            crypto_messages = []
            normal_messages = []

            for i, classification in enumerate(message_classifications):
                if i < len(state.messages):
                    message = state.messages[i]
                    if classification.get('is_crypto', False) or classification.get('crypto_confidence', 0) > 0.6:
                        crypto_messages.append(message)
                    else:
                        normal_messages.append(message)

            # Process crypto messages if any
            crypto_result = {}
            if crypto_messages:
                crypto_workflow = CryptoAnalysisWorkflow("mixed_crypto_sub_workflow")
                crypto_input_state = {
                    "messages": crypto_messages,
                    "context": state.context
                }
                crypto_result = await crypto_workflow.invoke(crypto_input_state)

            # Process normal messages if any
            normal_result = {}
            if normal_messages:
                normal_workflow = TelegramMessageProcessingWorkflow("mixed_normal_sub_workflow")
                normal_input_state = {
                    "messages": normal_messages,
                    "context": state.context
                }
                normal_result = await normal_workflow.invoke(normal_input_state)

            return {
                **state,
                "processing_pipeline": "mixed",
                "crypto_processing_result": crypto_result,
                "normal_processing_result": normal_result,
                "crypto_messages_count": len(crypto_messages),
                "normal_messages_count": len(normal_messages),
                "next_action": "harmonize_output"
            }

        except Exception as e:
            self.logger.error(f"Error in mixed content processing: {str(e)}")
            return {
                **state,
                "processing_pipeline": "mixed",
                "crypto_processing_result": {},
                "normal_processing_result": {},
                "processing_error": str(e),
                "next_action": "harmonize_output"
            }

    async def _harmonize_output(self, state: GraphState) -> Dict[str, Any]:
        """
        Harmonize output from different processing pipelines into a consistent format.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Final harmonized state
        """
        try:
            processing_pipeline = state.get('processing_pipeline', 'unknown')

            self.logger.debug(f"Harmonizing output from {processing_pipeline} pipeline")

            # Create unified output structure
            harmonized_output = {
                "success": True,
                "processing_pipeline": processing_pipeline,
                "content_type": state.get('content_type', 'unknown'),
                "crypto_ratio": state.get('crypto_ratio', 0.0),
                "crypto_confidence": state.get('crypto_confidence', 0.0),
                "total_messages": len(state.messages),
                "processing_metadata": {
                    "workflow_name": self.name,
                    "routing_decision": state.get('routing_decision', processing_pipeline),
                    "message_classifications": state.get('message_classifications', [])
                }
            }

            # Add pipeline-specific results
            if processing_pipeline == "crypto":
                harmonized_output.update({
                    "crypto_signals": state.get('crypto_signals', []),
                    "market_analysis": state.get('market_analysis', {}),
                    "generated_content": state.get('unique_content', ''),
                    "content_type_specific": "crypto_analysis"
                })
            elif processing_pipeline == "normal":
                harmonized_output.update({
                    "response": state.get('response', ''),
                    "analysis_result": state.get('analysis_result', {}),
                    "generated_content": state.get('response', ''),
                    "content_type_specific": "normal_processing"
                })
            elif processing_pipeline == "mixed":
                # Combine results from both pipelines
                crypto_result = state.get('crypto_processing_result', {})
                normal_result = state.get('normal_processing_result', {})

                harmonized_output.update({
                    "crypto_signals": crypto_result.get('crypto_signals', []),
                    "market_analysis": crypto_result.get('market_analysis', {}),
                    "response": normal_result.get('response', ''),
                    "analysis_result": normal_result.get('analysis_result', {}),
                    "generated_content": self._combine_mixed_content(crypto_result, normal_result),
                    "content_type_specific": "mixed_processing",
                    "crypto_messages_count": state.get('crypto_messages_count', 0),
                    "normal_messages_count": state.get('normal_messages_count', 0)
                })

            # Add error information if present
            if 'processing_error' in state:
                harmonized_output.update({
                    "success": False,
                    "error": state['processing_error'],
                    "generated_content": f"Processing error: {state['processing_error']}"
                })

            self.logger.info(f"Successfully harmonized output from {processing_pipeline} pipeline")

            return {
                **state,
                "harmonized_output": harmonized_output,
                "final_result": harmonized_output,
                "is_complete": True
            }

        except Exception as e:
            self.logger.error(f"Error in output harmonization: {str(e)}")
            return {
                **state,
                "harmonized_output": {
                    "success": False,
                    "error": f"Harmonization error: {str(e)}",
                    "processing_pipeline": state.get('processing_pipeline', 'unknown'),
                    "generated_content": f"Error harmonizing output: {str(e)}"
                },
                "is_complete": True
            }

    def _combine_mixed_content(self, crypto_result: Dict[str, Any], normal_result: Dict[str, Any]) -> str:
        """
        Combine content from crypto and normal processing results.

        Args:
            crypto_result: Results from crypto processing
            normal_result: Results from normal processing

        Returns:
            str: Combined content
        """
        try:
            combined_parts = []

            # Add crypto content if available
            crypto_content = crypto_result.get('unique_content', '')
            if crypto_content and crypto_content.strip():
                combined_parts.append(f"**Crypto Analysis:**\n{crypto_content}")

            # Add normal content if available
            normal_content = normal_result.get('response', '')
            if normal_content and normal_content.strip():
                combined_parts.append(f"**General Analysis:**\n{normal_content}")

            # Combine with separator
            if combined_parts:
                return "\n\n---\n\n".join(combined_parts)
            else:
                return "No content generated from mixed processing"

        except Exception as e:
            self.logger.error(f"Error combining mixed content: {str(e)}")
            return f"Error combining content: {str(e)}"
            
        except Exception as e:
            self.logger.error(f"Error setting up content generation workflow for '{self.name}': {str(e)}")
            raise
            
    async def _define_content_topic(self, state: GraphState) -> Dict[str, Any]:
        """
        Define the content topic.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with content topic
        """
        try:
            context = state.context
            content_topic = state.content_topic or context.get("topic", "General Topic")
            
            self.logger.debug(f"Defining content topic: {content_topic}")
            
            return {
                "context": context,
                "content_topic": content_topic,
                "next_action": "select_style"
            }
            
        except Exception as e:
            self.logger.error(f"Error in defining content topic: {str(e)}")
            return {
                "context": state.context,
                "content_topic": "Error defining topic",
                "next_action": "end",
                "is_complete": True
            }
            
    async def _select_content_style(self, state: GraphState) -> Dict[str, Any]:
        """
        Select the content style.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with content style
        """
        try:
            context = state.context
            content_topic = state.content_topic
            content_type = state.content_type or context.get("content_type", "article")
            
            self.logger.debug(f"Selecting content style for {content_type} on {content_topic}")
            
            # Define style based on content type
            style_guides = {
                "article": {
                    "tone": "informative",
                    "length": "long-form",
                    "structure": "introduction, body, conclusion",
                    "audience": "general"
                },
                "social_post": {
                    "tone": "engaging",
                    "length": "short",
                    "structure": "hook, main point, call-to-action",
                    "audience": "social media users"
                },
                "report": {
                    "tone": "professional",
                    "length": "detailed",
                    "structure": "executive summary, findings, recommendations",
                    "audience": "business professionals"
                },
                "analysis": {
                    "tone": "analytical",
                    "length": "medium to long",
                    "structure": "thesis, evidence, conclusion",
                    "audience": "analysts and researchers"
                }
            }
            
            content_style = style_guides.get(content_type, style_guides["article"])
            
            return {
                "context": context,
                "content_topic": content_topic,
                "content_type": content_type,
                "content_style": content_style,
                "next_action": "generate_content"
            }
            
        except Exception as e:
            self.logger.error(f"Error in selecting content style: {str(e)}")
            return {
                "context": state.context,
                "content_topic": state.content_topic,
                "content_type": state.content_type or "article",
                "content_style": {},
                "next_action": "end",
                "is_complete": True
            }
            
    async def _generate_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Generate the content with enhanced validation to prevent fake trading signals.

        Args:
            state: Current workflow state

        Returns:
            Dict[str, Any]: Updated state with generated content
        """
        try:
            context = state.context
            content_topic = state.content_topic
            content_type = state.content_type
            content_style = state.content_style

            self.logger.debug(f"Generating {content_type} content on {content_topic}")

            # Enhanced content generation with fake content prevention
            content_templates = {
                "article": """
{topic} - Market Analysis
========================

Overview
--------
This analysis examines {topic} based on available market data and trends.

Key Observations
---------------
Market participants should consider multiple factors when evaluating {topic}, including:
• Historical performance patterns
• Current market conditions
• Risk management considerations

Important Disclaimer
-------------------
This content is for informational purposes only and should not be considered as financial advice.
Always conduct your own research and consult with qualified professionals before making investment decisions.
                """,
                "social_post": """
📊 Market Update: {topic}

Key points to consider:
• Market analysis based on available data
• Multiple factors influence outcomes
• Always DYOR (Do Your Own Research)

⚠️ Not financial advice
#MarketAnalysis #Research #DYOR
                """,
                "report": """
MARKET RESEARCH REPORT: {topic}
===============================

EXECUTIVE SUMMARY
=================
This report provides an analytical overview of {topic} based on publicly available information.

METHODOLOGY
===========
• Data collection from multiple sources
• Technical and fundamental analysis
• Risk assessment framework

KEY FINDINGS
============
1. Market conditions vary significantly
2. Multiple factors influence outcomes
3. Risk management is essential

DISCLAIMER
==========
This report is for educational purposes only. Not financial advice.
                """,
                "analysis": """
Technical Analysis: {topic}
===========================

Analysis Framework
------------------
This analysis examines {topic} using established analytical methods.

Observations
------------
Based on available data:
1. Market trends show variability
2. Multiple indicators suggest mixed signals
3. Risk factors should be carefully considered

Risk Disclaimer
---------------
All analysis is subject to market volatility and uncertainty. This is not financial advice.
                """
            }

            # Prevent generation of fake trading signals
            if any(term in content_topic.lower() for term in ['guaranteed', '100%', 'risk-free', 'sure profit', 'moon', 'lambo']):
                self.logger.warning(f"Preventing generation of potentially misleading content for topic: {content_topic}")
                generated_content = f"""
Content Generation Restricted
============================

The requested topic "{content_topic}" contains terms that could lead to misleading financial content.

For responsible content creation, please consider:
• Educational market analysis
• Risk-aware investment discussions
• Balanced perspective on market opportunities

This restriction helps maintain content quality and prevents potentially harmful financial misinformation.
                """
            else:
                template = content_templates.get(content_type, content_templates["article"])
                generated_content = template.replace("{topic}", content_topic)

            # Validate generated content
            from helpers.validation_helpers import detect_fake_content, detect_roleplay_content

            fake_detection = detect_fake_content(generated_content)
            roleplay_detection = detect_roleplay_content(generated_content)

            # Add validation warnings to context
            if fake_detection["is_fake"] or roleplay_detection["is_roleplay"]:
                context["content_validation"] = {
                    "fake_detection": fake_detection,
                    "roleplay_detection": roleplay_detection,
                    "validation_passed": False
                }
                self.logger.warning("Generated content failed validation checks")
            else:
                context["content_validation"] = {
                    "validation_passed": True
                }

            return {
                "context": context,
                "content_topic": content_topic,
                "content_type": content_type,
                "content_style": content_style,
                "generated_content": generated_content,
                "next_action": "review_content"
            }

        except Exception as e:
            self.logger.error(f"Error in content generation: {str(e)}")
            return {
                "context": state.context,
                "content_topic": state.content_topic,
                "content_type": state.content_type,
                "content_style": state.content_style,
                "generated_content": "Error generating content",
                "next_action": "end",
                "is_complete": True
            }
            
    async def _review_content(self, state: GraphState) -> Dict[str, Any]:
        """
        Review the generated content.
        
        Args:
            state: Current workflow state
            
        Returns:
            Dict[str, Any]: Updated state with reviewed content
        """
        try:
            context = state.context
            content_topic = state.content_topic
            content_type = state.content_type
            content_style = state.content_style
            generated_content = state.generated_content
            
            self.logger.debug("Reviewing generated content")
            
            # Content review logic would go here
            # This is a placeholder implementation
            reviewed_content = generated_content  # In a real implementation, this would be reviewed
            
            return {
                "context": context,
                "content_topic": content_topic,
                "content_type": content_type,
                "content_style": content_style,
                "generated_content": reviewed_content,
                "is_complete": True
            }
            
        except Exception as e:
            self.logger.error(f"Error in content review: {str(e)}")
            return {
                "context": state.context,
                "content_topic": state.content_topic,
                "content_type": state.content_type,
                "content_style": state.content_style,
                "generated_content": "Error reviewing content",
                "is_complete": True
            }