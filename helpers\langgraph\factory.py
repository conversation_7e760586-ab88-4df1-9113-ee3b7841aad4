"""
Factory for creating Langgraph workflow instances.
"""

import logging
from typing import Dict, Any, Optional, Callable
from utils.logger import Logger
from helpers.langgraph.core import MessageProcessingWorkflow, CryptoAnalysisWorkflow, LanggraphWorkflow, TelegramMessageProcessingWorkflow, ContentGenerationWorkflow, MasterContentWorkflow

# Initialize logger
langgraph_factory_logger = Logger("langgraph_factory")

def create_workflow(workflow_type: str = "default", name: str = "workflow", 
                   config: Optional[Dict[str, Any]] = None) -> LanggraphWorkflow:
    """
    Create a Langgraph workflow based on the specified type.

    Args:
        workflow_type: Type of workflow to create ("default", "message_processing", "telegram", "crypto", "content", "master", etc.)
        name: Name of the workflow
        config: Configuration for the workflow

    Returns:
        LanggraphWorkflow: Created workflow
    """
    try:
        if workflow_type == "master":
            workflow = MasterContentWorkflow(name)
            langgraph_factory_logger.info("Created master content workflow", name=name)
        elif workflow_type == "message_processing":
            workflow = MessageProcessingWorkflow(name)
            langgraph_factory_logger.info("Created message processing workflow", name=name)
        elif workflow_type == "telegram":
            workflow = TelegramMessageProcessingWorkflow(name)
            langgraph_factory_logger.info("Created Telegram message processing workflow", name=name)
        elif workflow_type == "crypto":
            workflow = CryptoAnalysisWorkflow(name)
            langgraph_factory_logger.info("Created crypto analysis workflow", name=name)
        elif workflow_type == "content":
            workflow = ContentGenerationWorkflow(name)
            langgraph_factory_logger.info("Created content generation workflow", name=name)
        else:
            workflow = LanggraphWorkflow(name)
            langgraph_factory_logger.info("Created default workflow", name=name)
            
        return workflow
        
    except Exception as e:
        langgraph_factory_logger.error("Error creating workflow", workflow_name=name, workflow_type=workflow_type, error=str(e))
        raise

def create_custom_workflow(name: str, nodes: Dict[str, Callable], 
                          edges: list, entry_point: str, 
                          finish_point: str) -> LanggraphWorkflow:
    """
    Create a custom Langgraph workflow with specified nodes and edges.
    
    Args:
        name: Name of the workflow
        nodes: Dictionary of node names and their corresponding functions
        edges: List of tuples representing edges (start_node, end_node)
        entry_point: Name of the entry point node
        finish_point: Name of the finish point node
        
    Returns:
        LanggraphWorkflow: Created custom workflow
    """
    try:
        workflow = LanggraphWorkflow(name)
        
        # Add nodes
        for node_name, action in nodes.items():
            workflow.add_node(node_name, action)
            
        # Add edges
        for start_node, end_node in edges:
            workflow.add_edge(start_node, end_node)
            
        # Set entry and finish points
        workflow.set_entry_point(entry_point)
        workflow.set_finish_point(finish_point)
        
        langgraph_factory_logger.info("Created custom workflow", name=name)
        return workflow
        
    except Exception as e:
        langgraph_factory_logger.error("Error creating custom workflow", workflow_name=name, error=str(e))
        raise

def create_telegram_workflow(name: str = "telegram_workflow", 
                           workflow_type: str = "telegram") -> TelegramMessageProcessingWorkflow:
    """
    Create a Telegram-specific workflow.
    
    Args:
        name: Name of the workflow
        workflow_type: Type of Telegram workflow ("telegram", "message_processing", etc.)
        
    Returns:
        TelegramMessageProcessingWorkflow: Created Telegram workflow
    """
    try:
        if workflow_type == "telegram":
            workflow = TelegramMessageProcessingWorkflow(name)
        else:
            workflow = MessageProcessingWorkflow(name)
            
        langgraph_factory_logger.info("Created Telegram workflow", name=name)
        return workflow
        
    except Exception as e:
        langgraph_factory_logger.error("Error creating Telegram workflow", workflow_name=name, error=str(e))
        raise

def create_crypto_analysis_workflow(name: str = "crypto_analysis_workflow") -> CryptoAnalysisWorkflow:
    """
    Create a crypto analysis workflow.
    
    Args:
        name: Name of the workflow
        
    Returns:
        CryptoAnalysisWorkflow: Created crypto analysis workflow
    """
    try:
        workflow = CryptoAnalysisWorkflow(name)
        langgraph_factory_logger.info("Created crypto analysis workflow", name=name)
        return workflow
        
    except Exception as e:
        langgraph_factory_logger.error("Error creating crypto analysis workflow", workflow_name=name, error=str(e))
        raise

def create_content_generation_workflow(name: str = "content_generation_workflow") -> ContentGenerationWorkflow:
    """
    Create a content generation workflow.
    
    Args:
        name: Name of the workflow
        
    Returns:
        ContentGenerationWorkflow: Created content generation workflow
    """
    try:
        workflow = ContentGenerationWorkflow(name)
        langgraph_factory_logger.info("Created content generation workflow", name=name)
        return workflow
        
    except Exception as e:
        langgraph_factory_logger.error("Error creating content generation workflow", workflow_name=name, error=str(e))
        raise

def create_master_content_workflow(name: str = "master_content_workflow") -> MasterContentWorkflow:
    """
    Create a master content workflow that automatically routes content to appropriate pipelines.

    Args:
        name: Name of the workflow

    Returns:
        MasterContentWorkflow: Created master content workflow
    """
    try:
        workflow = MasterContentWorkflow(name)
        langgraph_factory_logger.info("Created master content workflow", name=name)
        return workflow

    except Exception as e:
        langgraph_factory_logger.error("Error creating master content workflow", workflow_name=name, error=str(e))
        raise
