# Master Content Workflow

The Master Content Workflow provides a unified entry point for all content processing in the Telegram scraper application. It automatically detects content type and routes messages to the appropriate specialized processing pipelines.

## Overview

The Master Content Workflow eliminates the need to manually choose between crypto and normal content processing workflows. It:

1. **Automatically classifies** incoming content as crypto-related, normal, or mixed
2. **Intelligently routes** content to the appropriate specialized pipeline
3. **Harmonizes output** from different pipelines into a consistent format
4. **Provides metadata** about routing decisions and processing paths

## Architecture

```
Input Messages
      ↓
Content Classification
      ↓
Intelligent Routing
      ↓
┌─────────────┬─────────────┬─────────────┐
│   Crypto    │   Normal    │   Mixed     │
│  Pipeline   │  Pipeline   │  Pipeline   │
└─────────────┴─────────────┴─────────────┘
      ↓
Output Harmonization
      ↓
Unified Response
```

## Key Components

### 1. Content Classification (`_classify_content`)

Analyzes incoming messages to determine content type:

- **Crypto Detection**: Uses keyword patterns and confidence scoring
- **Confidence Calculation**: Weighted scoring based on crypto-related terms
- **Ratio Analysis**: Determines overall crypto vs normal content ratio
- **Classification Logic**: Configurable thresholds for routing decisions

### 2. Intelligent Routing (`_route_content`)

Routes content based on classification results:

- **Crypto Pipeline**: For high crypto confidence/ratio content
- **Normal Pipeline**: For low crypto confidence content  
- **Mixed Pipeline**: For content with mixed crypto/normal messages
- **Fallback Logic**: Configurable fallback behavior on errors

### 3. Specialized Processing

- **Crypto Processing**: Uses `CryptoAnalysisWorkflow` for signal extraction and market analysis
- **Normal Processing**: Uses `TelegramMessageProcessingWorkflow` for standard message processing
- **Mixed Processing**: Separates and processes crypto/normal messages independently

### 4. Output Harmonization (`_harmonize_output`)

Creates consistent output format regardless of processing pipeline:

- **Unified Structure**: Standard response format across all pipelines
- **Processing Metadata**: Information about routing decisions and pipeline used
- **Content Combination**: Intelligent merging of mixed processing results
- **Error Handling**: Graceful error reporting and fallback responses

## Configuration

Configure the master workflow in `config.json`:

```json
{
  "master_workflow": {
    "enable_unified_processing": true,
    "crypto_detection_threshold": 0.6,
    "crypto_ratio_threshold": 0.8,
    "mixed_content_threshold": 0.2,
    "default_pipeline": "master",
    "fallback_pipeline": "normal",
    "enable_pipeline_logging": true,
    "routing_preferences": {
      "prefer_crypto_pipeline": false,
      "enable_mixed_processing": true,
      "strict_crypto_classification": false
    }
  }
}
```

### Configuration Options

- **`crypto_detection_threshold`**: Minimum confidence score to classify individual messages as crypto (0.0-1.0)
- **`crypto_ratio_threshold`**: Minimum ratio of crypto messages to route to crypto pipeline (0.0-1.0)
- **`mixed_content_threshold`**: Maximum ratio for normal classification (0.0-1.0)
- **`fallback_pipeline`**: Pipeline to use on errors ("crypto", "normal", "mixed")
- **`prefer_crypto_pipeline`**: Prefer crypto pipeline for ambiguous content
- **`enable_mixed_processing`**: Allow mixed content processing (if false, routes to preferred pipeline)
- **`strict_crypto_classification`**: Use stricter thresholds for classification

## Usage

### Through LanggraphService (Recommended)

```python
from services.langgraph_service import LanggraphService

service = LanggraphService()

# Process messages through unified workflow
messages = [
    {"content": "Bitcoin analysis shows bullish trend"},
    {"content": "Great weather today!"}
]

result = await service.process_messages_unified(messages)

print(f"Pipeline used: {result['processing_pipeline']}")
print(f"Content type: {result['content_type']}")
print(f"Success: {result['success']}")
```

### Direct Workflow Usage

```python
from helpers.langgraph.factory import create_master_content_workflow

# Create workflow
workflow = create_master_content_workflow("my_master_workflow")

# Prepare input
input_state = {
    "messages": [{"content": "ETH showing strong signals"}],
    "context": {"source": "telegram"}
}

# Run workflow
result = await workflow.invoke(input_state)
harmonized_output = result['harmonized_output']
```

### Through Factory

```python
from helpers.langgraph.factory import create_workflow

# Create master workflow through factory
workflow = create_workflow("master", "unified_processor")
```

## Output Format

The master workflow produces a harmonized output structure:

```python
{
    "success": True,
    "processing_pipeline": "crypto|normal|mixed",
    "content_type": "crypto|normal|mixed", 
    "crypto_ratio": 0.75,
    "crypto_confidence": 0.82,
    "total_messages": 5,
    "generated_content": "...",
    "processing_metadata": {
        "workflow_name": "master_content_workflow",
        "routing_decision": "crypto",
        "message_classifications": [...]
    },
    "validation_result": {
        "original_count": 5,
        "validated_count": 4,
        "filtered_count": 1
    }
}
```

### Pipeline-Specific Fields

**Crypto Pipeline:**
- `crypto_signals`: Extracted trading signals
- `market_analysis`: Market condition analysis
- `content_type_specific`: "crypto_analysis"

**Normal Pipeline:**
- `response`: Generated response
- `analysis_result`: Analysis results
- `content_type_specific`: "normal_processing"

**Mixed Pipeline:**
- `crypto_signals`: From crypto messages
- `response`: From normal messages
- `crypto_messages_count`: Number of crypto messages processed
- `normal_messages_count`: Number of normal messages processed
- `content_type_specific`: "mixed_processing"

## Benefits

1. **Simplified Integration**: Single entry point for all content processing
2. **Automatic Routing**: No manual pipeline selection required
3. **Consistent Output**: Unified response format across all pipelines
4. **Configurable Behavior**: Flexible routing and classification options
5. **Comprehensive Metadata**: Full visibility into processing decisions
6. **Error Resilience**: Graceful fallback handling
7. **Performance Optimized**: Efficient content classification and routing

## Testing

Run the comprehensive test suite:

```bash
python -m pytest tests/test_master_workflow.py -v
```

See the demonstration script:

```bash
python examples/master_workflow_demo.py
```

## Migration Guide

### From Separate Workflows

**Before:**
```python
# Manual workflow selection
if is_crypto_content(messages):
    workflow = create_crypto_analysis_workflow()
else:
    workflow = create_telegram_workflow()
```

**After:**
```python
# Automatic routing
service = LanggraphService()
result = await service.process_messages_unified(messages)
```

### Configuration Migration

Update your `config.json` to include the `master_workflow` section with desired routing preferences and thresholds.

## Troubleshooting

### Common Issues

1. **Incorrect Routing**: Adjust `crypto_detection_threshold` and `crypto_ratio_threshold`
2. **Mixed Content Not Processed**: Ensure `enable_mixed_processing` is `true`
3. **Fallback Behavior**: Check `fallback_pipeline` configuration
4. **Performance Issues**: Enable `enable_pipeline_logging` for debugging

### Debug Logging

Enable detailed logging in configuration:

```json
{
  "master_workflow": {
    "enable_pipeline_logging": true
  }
}
```

This will log routing decisions, confidence scores, and pipeline selections for troubleshooting.
