"""
Demonstration of the Master Content Workflow integration.
Shows how the unified entry point automatically routes content to appropriate pipelines.
"""

import asyncio
import json
from typing import List, Dict, Any
from services.langgraph_service import LanggraphService
from helpers.langgraph.factory import create_master_content_workflow
from utils.logger import Logger

# Initialize logger
demo_logger = Logger("master_workflow_demo")


async def demo_crypto_content_processing():
    """Demonstrate processing of crypto-related content."""
    demo_logger.info("=== Crypto Content Processing Demo ===")
    
    # Sample crypto messages
    crypto_messages = [
        {"content": "Bitcoin BTC analysis: Strong bullish momentum, target $50000, stop loss $42000"},
        {"content": "Ethereum ETH showing breakout pattern, buy signal confirmed"},
        {"content": "Trading alert: SOL pump incoming, entry at $185, target $220"}
    ]
    
    # Create LanggraphService instance
    service = LanggraphService()
    
    try:
        # Process messages through unified workflow
        result = await service.process_messages_unified(crypto_messages)
        
        demo_logger.info("Crypto processing result:")
        demo_logger.info(f"  - Success: {result.get('success', False)}")
        demo_logger.info(f"  - Pipeline: {result.get('processing_pipeline', 'unknown')}")
        demo_logger.info(f"  - Content Type: {result.get('content_type', 'unknown')}")
        demo_logger.info(f"  - Crypto Ratio: {result.get('crypto_ratio', 0):.2f}")
        demo_logger.info(f"  - Crypto Confidence: {result.get('crypto_confidence', 0):.2f}")
        
        if result.get('crypto_signals'):
            demo_logger.info(f"  - Crypto Signals Found: {len(result['crypto_signals'])}")
        
        return result
        
    except Exception as e:
        demo_logger.error(f"Error in crypto content processing: {str(e)}")
        return None


async def demo_normal_content_processing():
    """Demonstrate processing of normal content."""
    demo_logger.info("=== Normal Content Processing Demo ===")
    
    # Sample normal messages
    normal_messages = [
        {"content": "Beautiful sunset today, perfect weather for a walk in the park"},
        {"content": "Just finished reading an amazing book about ancient history"},
        {"content": "Planning a weekend trip to the mountains with family"}
    ]
    
    # Create LanggraphService instance
    service = LanggraphService()
    
    try:
        # Process messages through unified workflow
        result = await service.process_messages_unified(normal_messages)
        
        demo_logger.info("Normal processing result:")
        demo_logger.info(f"  - Success: {result.get('success', False)}")
        demo_logger.info(f"  - Pipeline: {result.get('processing_pipeline', 'unknown')}")
        demo_logger.info(f"  - Content Type: {result.get('content_type', 'unknown')}")
        demo_logger.info(f"  - Crypto Ratio: {result.get('crypto_ratio', 0):.2f}")
        demo_logger.info(f"  - Crypto Confidence: {result.get('crypto_confidence', 0):.2f}")
        
        if result.get('response'):
            demo_logger.info(f"  - Response Generated: Yes")
        
        return result
        
    except Exception as e:
        demo_logger.error(f"Error in normal content processing: {str(e)}")
        return None


async def demo_mixed_content_processing():
    """Demonstrate processing of mixed content."""
    demo_logger.info("=== Mixed Content Processing Demo ===")
    
    # Sample mixed messages
    mixed_messages = [
        {"content": "Bitcoin is interesting but I also love cooking Italian pasta"},
        {"content": "The weather forecast shows rain tomorrow"},
        {"content": "ETH price analysis indicates potential growth to $3500"},
        {"content": "My favorite movie is about space exploration and aliens"},
        {"content": "Trading signal: Long BTC at current levels"}
    ]
    
    # Create LanggraphService instance
    service = LanggraphService()
    
    try:
        # Process messages through unified workflow
        result = await service.process_messages_unified(mixed_messages)
        
        demo_logger.info("Mixed processing result:")
        demo_logger.info(f"  - Success: {result.get('success', False)}")
        demo_logger.info(f"  - Pipeline: {result.get('processing_pipeline', 'unknown')}")
        demo_logger.info(f"  - Content Type: {result.get('content_type', 'unknown')}")
        demo_logger.info(f"  - Crypto Ratio: {result.get('crypto_ratio', 0):.2f}")
        demo_logger.info(f"  - Crypto Confidence: {result.get('crypto_confidence', 0):.2f}")
        
        if result.get('crypto_messages_count'):
            demo_logger.info(f"  - Crypto Messages: {result['crypto_messages_count']}")
        if result.get('normal_messages_count'):
            demo_logger.info(f"  - Normal Messages: {result['normal_messages_count']}")
        
        return result
        
    except Exception as e:
        demo_logger.error(f"Error in mixed content processing: {str(e)}")
        return None


async def demo_direct_workflow_usage():
    """Demonstrate direct usage of the master workflow."""
    demo_logger.info("=== Direct Master Workflow Usage Demo ===")
    
    try:
        # Create master workflow directly
        workflow = create_master_content_workflow("demo_master_workflow")
        
        # Sample input state
        input_state = {
            "messages": [
                {"content": "Solana SOL showing strong bullish signals, target $250"},
                {"content": "Great weather for hiking in the mountains today"}
            ],
            "context": {
                "source": "demo",
                "timestamp": "2023-01-01T00:00:00Z"
            }
        }
        
        # Run workflow
        result = await workflow.invoke(input_state)
        
        demo_logger.info("Direct workflow result:")
        demo_logger.info(f"  - Workflow Complete: {result.get('is_complete', False)}")
        
        harmonized_output = result.get('harmonized_output', {})
        demo_logger.info(f"  - Success: {harmonized_output.get('success', False)}")
        demo_logger.info(f"  - Pipeline: {harmonized_output.get('processing_pipeline', 'unknown')}")
        demo_logger.info(f"  - Content Type: {harmonized_output.get('content_type', 'unknown')}")
        
        # Show processing metadata
        metadata = harmonized_output.get('processing_metadata', {})
        demo_logger.info(f"  - Routing Decision: {metadata.get('routing_decision', 'unknown')}")
        demo_logger.info(f"  - Total Messages: {harmonized_output.get('total_messages', 0)}")
        
        return result
        
    except Exception as e:
        demo_logger.error(f"Error in direct workflow usage: {str(e)}")
        return None


async def demo_configuration_impact():
    """Demonstrate how configuration affects routing decisions."""
    demo_logger.info("=== Configuration Impact Demo ===")
    
    # This would show how different config settings affect routing
    # For demo purposes, we'll just log the current configuration
    from utils.config_loader import ConfigLoader
    
    try:
        config = ConfigLoader.load_config()
        master_config = config.get('master_workflow', {})
        
        demo_logger.info("Current master workflow configuration:")
        demo_logger.info(f"  - Unified Processing: {master_config.get('enable_unified_processing', False)}")
        demo_logger.info(f"  - Crypto Detection Threshold: {master_config.get('crypto_detection_threshold', 0.6)}")
        demo_logger.info(f"  - Crypto Ratio Threshold: {master_config.get('crypto_ratio_threshold', 0.8)}")
        demo_logger.info(f"  - Mixed Content Threshold: {master_config.get('mixed_content_threshold', 0.2)}")
        demo_logger.info(f"  - Default Pipeline: {master_config.get('default_pipeline', 'master')}")
        demo_logger.info(f"  - Fallback Pipeline: {master_config.get('fallback_pipeline', 'normal')}")
        
        routing_prefs = master_config.get('routing_preferences', {})
        demo_logger.info("Routing preferences:")
        demo_logger.info(f"  - Prefer Crypto Pipeline: {routing_prefs.get('prefer_crypto_pipeline', False)}")
        demo_logger.info(f"  - Enable Mixed Processing: {routing_prefs.get('enable_mixed_processing', True)}")
        demo_logger.info(f"  - Strict Classification: {routing_prefs.get('strict_crypto_classification', False)}")
        
    except Exception as e:
        demo_logger.error(f"Error loading configuration: {str(e)}")


async def main():
    """Run all demonstration scenarios."""
    demo_logger.info("Starting Master Content Workflow Demonstration")
    demo_logger.info("=" * 60)
    
    try:
        # Show current configuration
        await demo_configuration_impact()
        print()
        
        # Demo crypto content processing
        crypto_result = await demo_crypto_content_processing()
        print()
        
        # Demo normal content processing
        normal_result = await demo_normal_content_processing()
        print()
        
        # Demo mixed content processing
        mixed_result = await demo_mixed_content_processing()
        print()
        
        # Demo direct workflow usage
        direct_result = await demo_direct_workflow_usage()
        print()
        
        demo_logger.info("=" * 60)
        demo_logger.info("Master Content Workflow Demonstration Complete")
        
        # Summary
        results = [crypto_result, normal_result, mixed_result, direct_result]
        successful_demos = sum(1 for r in results if r and r.get('success', False))
        demo_logger.info(f"Successful demonstrations: {successful_demos}/{len(results)}")
        
    except Exception as e:
        demo_logger.error(f"Error in main demonstration: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
