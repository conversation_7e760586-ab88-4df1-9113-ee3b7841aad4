"""
Langgraph service for processing messages with Langgraph workflows.
Enhanced with fake content detection and validation.
"""

import asyncio
import traceback
from typing import Dict, Any, Optional, List
from utils.logger import Logger
from utils.config_loader import ConfigLoader
from helpers.langgraph.factory import create_workflow, create_master_content_workflow, create_custom_workflow, create_custom_workflow
from helpers.langgraph.content_validator import LanggraphContentValidator

# Initialize logger
langgraph_service_logger = Logger("langgraph_service")

class LanggraphService:
    """Service for Langgraph operations"""
    
    def __init__(self):
        """Initialize the Langgraph service with enhanced content validation"""
        self.langgraph_service_logger = langgraph_service_logger
        # Load configuration for content validation
        self.config = ConfigLoader.load_config()
        # Initialize content validator
        self.content_validator = LanggraphContentValidator(self.config)
        
    async def create_and_run_workflow(self, workflow_type: str = "master",
                                     name: str = "workflow",
                                     input_state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create and run a Langgraph workflow.

        Args:
            workflow_type: Type of workflow to create (defaults to "master" for unified processing)
            name: Name of the workflow
            input_state: Initial state for the workflow

        Returns:
            Dict[str, Any]: Final state after workflow execution
        """
        try:
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Creating and running workflow")
                section.log_step(f"Workflow details - Name: {name}, Type: {workflow_type}")

            if input_state is None:
                input_state = {}

            # Create workflow - use master workflow by default for unified processing
            if workflow_type == "master":
                workflow = create_master_content_workflow(name)
            else:
                workflow = create_workflow(workflow_type, name)

            # Run workflow
            result = await workflow.invoke(input_state)
            
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Successfully executed workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            return {
                'success': True,
                'workflow_name': name,
                'result': result
            }
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Error creating and running workflow")
                section.log_step(f"Error details - Name: {name}, Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': name,
                'result': None
            }
            
    async def run_message_processing_workflow(self, messages: List[Dict[str, str]],
                                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a message processing workflow with enhanced content validation.

        Args:
            messages: List of messages to process
            context: Additional context for processing, including date_range if needed

        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Running message processing workflow with content validation")
                section.log_step(f"Original message count: {len(messages)}")

            if context is None:
                context = {}

            # Validate messages before processing
            validation_result = self.content_validator.validate_message_batch(messages)
            validated_messages = validation_result["validated_messages"]

            # Log validation results
            section.log_step(f"Validation results - Original: {validation_result['original_count']}, "
                           f"Validated: {validation_result['validated_count']}, "
                           f"Filtered: {validation_result['filtered_count']}")

            if validation_result["warnings"]:
                section.log_step(f"Content validation warnings: {len(validation_result['warnings'])}")
                for warning in validation_result["warnings"][:5]:  # Log first 5 warnings
                    section.log_step(f"Warning: {warning['type']} - {warning.get('reasons', [])}")

            # Add validation context
            context['content_validation'] = validation_result

            # Add date information to context if not present
            if 'processing_timestamp' not in context:
                from datetime import datetime
                context['processing_timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
            # Add date context information if available
            if 'date_context' not in context:
                if 'date_range' in context:
                    date_range = context['date_range']
                    start_date = date_range.get('start')
                    end_date = date_range.get('end')
                    if start_date and end_date:
                        context['date_context'] = f"This analysis covers messages from {start_date} to {end_date}"
                    elif start_date:
                        context['date_context'] = f"This analysis covers messages from {start_date} onwards"
                    elif end_date:
                        context['date_context'] = f"This analysis covers messages up to {end_date}"
                    else:
                        context['date_context'] = "This analysis covers historical messages"
                else:
                    # Default to current timestamp
                    context['date_context'] = f"This analysis was performed on {context['processing_timestamp']}"
            
            # Process validated messages sequentially to ensure proper context handling
            processed_messages = []
            for message in validated_messages:
                # Prepare input state for each message
                input_state = {
                    "messages": [message],
                    "context": context.copy()  # Create a copy to avoid context pollution
                }

                # Determine workflow type based on context
                workflow_type = "telegram" if 'message_thread_id' in context or 'date_range' in context or 'date_context' in context else "message_processing"

                # Create and run message processing workflow for each message
                result = await self.create_and_run_workflow(
                    workflow_type=workflow_type,
                    name="message_processing_workflow",
                    input_state=input_state
                )

                processed_messages.append(result)
            
            # For backward compatibility, return the result of the last message processed
            final_result = processed_messages[-1] if processed_messages else {
                'success': False,
                'error': 'No messages to process',
                'workflow_name': 'message_processing_workflow',
                'result': None
            }
            
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Completed message processing workflow")
            
            return final_result
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Error in message processing workflow")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': 'message_processing_workflow',
                'result': None
            }
            
    async def run_custom_workflow(self, name: str, 
                                 nodes: Dict[str, Any],
                                 edges: list, 
                                 entry_point: str,
                                 finish_point: str,
                                 input_state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a custom Langgraph workflow.
        
        Args:
            name: Name of the workflow
            nodes: Dictionary of node names and their corresponding functions
            edges: List of tuples representing edges (start_node, end_node)
            entry_point: Name of the entry point node
            finish_point: Name of the finish point node
            input_state: Initial state for the workflow
            
        Returns:
            Dict[str, Any]: Final state after workflow execution
        """
        try:
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Running custom workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            if input_state is None:
                input_state = {}
                
            # Create custom workflow
            workflow = create_custom_workflow(name, nodes, edges, entry_point, finish_point)
            
            # Run workflow
            result = await workflow.invoke(input_state)
            
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Successfully executed custom workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            return {
                'success': True,
                'workflow_name': name,
                'result': result
            }
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Error running custom workflow")
                section.log_step(f"Error details - Name: {name}, Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': name,
                'result': None
            }

    async def process_messages_unified(self, messages: List[Dict[str, Any]],
                                     context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process messages using the unified master workflow that automatically detects
        and routes content to appropriate pipelines.

        Args:
            messages: List of messages to process
            context: Optional context for processing

        Returns:
            Dict[str, Any]: Unified processing result with metadata about routing decisions
        """
        try:
            with self.langgraph_service_logger.begin_section("Unified Message Processing") as section:
                section.log_step("Processing messages through unified master workflow")
                section.log_step(f"Message count: {len(messages)}")

            if context is None:
                context = {}

            # Validate messages using content validator
            validation_result = self.content_validator.validate_message_batch(messages)
            validated_messages = validation_result["validated_messages"]

            if not validated_messages:
                self.langgraph_service_logger.warning("No valid messages to process after validation")
                return {
                    'success': False,
                    'error': 'No valid messages after content validation',
                    'validation_result': validation_result,
                    'processing_pipeline': 'none'
                }

            # Prepare input state for master workflow
            input_state = {
                "messages": validated_messages,
                "context": context
            }

            # Create and run master workflow
            result = await self.create_and_run_workflow(
                workflow_type="master",
                name="unified_message_processing",
                input_state=input_state
            )

            # Extract harmonized output
            workflow_result = result.get('result', {})
            harmonized_output = workflow_result.get('harmonized_output', {})

            # Add validation metadata
            harmonized_output['validation_result'] = {
                'original_count': validation_result['original_count'],
                'validated_count': validation_result['validated_count'],
                'filtered_count': validation_result['filtered_count']
            }

            with self.langgraph_service_logger.begin_section("Unified Message Processing") as section:
                section.log_step("Successfully processed messages through unified workflow")
                section.log_step(f"Processing pipeline: {harmonized_output.get('processing_pipeline', 'unknown')}")
                section.log_step(f"Content type: {harmonized_output.get('content_type', 'unknown')}")

            return harmonized_output

        except Exception as e:
            self.langgraph_service_logger.error("Error in unified message processing", error=str(e))
            return {
                'success': False,
                'error': f'Unified processing error: {str(e)}',
                'processing_pipeline': 'error'
            }