"""
Langchain Processor Service
This service handles processing of scraped content using Langchain for active register records
during their specified posting hours.
"""
import asyncio
import re
import time
import json
import aiohttp
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from utils.logger import Logger
from services.database import DatabaseService
from services.llm_service import LLMService
from models.register import Register
from models.message import Message
from models.tg_content import TgContent  # Added import for TgContent
from helpers.register_helpers import get_register_tg_entities, get_register_by_project_id
from helpers.message_helpers import check_crawling_completion_status, is_message_unique
from helpers.bot_state_helpers import get_last_post_time
from helpers.message_formatter import format_message_link  # Added import
from helpers.validation_helpers import detect_fake_content, validate_trading_signal_content, is_content_sufficiently_detailed, detect_roleplay_content
from helpers.reply_queue_manager import reply_queue_manager
langchain_processor_logger = Logger("langchain_processor")

class LangchainProcessorService:
    """Service for processing scraped content using Langchain"""
    def __init__(self, db_service: DatabaseService, config: Dict[str, Any]):
        self.db_service = db_service
        self.config = config
        self.llm_service = LLMService()
        self.running = False
        self.processing_tasks = {}
        # Store the last processed message timestamp for each project
        self.last_processed_timestamps = {}
        # Configuration validation state
        self.config_validation_passed = False
        self.invalid_registers = set()  # Track registers with invalid config

    async def start(self):
        """Start the Langchain processor service"""
        # Create a single section for the entire method execution
        service_section = langchain_processor_logger.begin_section("Service Lifecycle")
        section = service_section.__enter__()
        section.log_step("Starting Langchain processor service")
        
        # Validate configuration before starting
        section.log_step("Validating Telegram configuration for all registers")
        await self._validate_telegram_configuration()
        
        # Close the section before setting the flag
        service_section.__exit__(None, None, None)
        self.running = True
        # Start the main processing loop
        self._processing_loop_task = asyncio.create_task(self._processing_loop())
        # Optionally await the task for a short time to ensure it starts
        try:
            await asyncio.wait_for(asyncio.shield(self._processing_loop_task), timeout=0.1)
        except asyncio.TimeoutError:
            # This is expected as the processing loop runs indefinitely
            pass

    async def stop(self):
        """Stop the Langchain processor service"""
        # Create a single section for the entire method execution
        service_section = langchain_processor_logger.begin_section("Service Lifecycle")
        section = service_section.__enter__()
        section.log_step("Stopping Langchain processor service")
        # Close the section before setting the flag
        service_section.__exit__(None, None, None)
        self.running = False
        # Cancel all running processing tasks
        for task in self.processing_tasks.values():
            task.cancel()
        # Cancel the main processing loop task
        if hasattr(self, '_processing_loop_task') and self._processing_loop_task:
            self._processing_loop_task.cancel()
        # Wait for tasks to complete
        tasks_to_wait = list(self.processing_tasks.values())
        if hasattr(self, '_processing_loop_task') and self._processing_loop_task:
            tasks_to_wait.append(self._processing_loop_task)
        if tasks_to_wait:
            await asyncio.gather(*tasks_to_wait, return_exceptions=True)

    async def _processing_loop(self):
        """Main processing loop"""
        while self.running:
            try:
                # Get current hour to check posting hours
                current_hour = datetime.now().hour
                # Create a single section for the entire method execution
                processing_section = langchain_processor_logger.begin_section("Content Processing")
                section = processing_section.__enter__()
                section.log_step("Finding active registers within posting hours")
                # Get all active users within their posting hours
                session = await self.db_service.get_session()
                try:
                    from sqlalchemy import select
                    stmt = select(Register).where(
                        Register.active == 1,
                        Register.active_post_hour_start <= current_hour,
                        Register.active_post_hour_end >= current_hour
                    )
                    result = await session.execute(stmt)
                    active_registers = result.scalars().all()
                    section.log_step(f"Found active registers within posting hours: {len(active_registers)}")
                    # Process content for each active register sequentially, not concurrently
                    for register in active_registers:
                        try:
                            await self._process_register_content(register)
                        except Exception as e:
                            section.log_step(f"Error processing content for register: {getattr(register, 'project_id', 'unknown')}")
                            section.log_step(f"Error details - Error: {str(e)}")
                finally:
                    await session.close()
                # Instead of fixed wait time, dynamically calculate wait time
                wait_time = await self._calculate_dynamic_wait_time()
                section.log_step(f"Waiting seconds before next processing cycle: {wait_time}")
                # Close the section before sleeping
                processing_section.__exit__(None, None, None)
                await asyncio.sleep(wait_time)
            except Exception as e:
                # Handle case where section might not be defined yet
                if 'section' in locals():
                    section.log_step("Error in processing loop")
                    section.log_step(f"Error details - Error: {str(e)}")
                    # Close the section before sleeping
                    processing_section.__exit__(None, None, None)
                # Wait 30 seconds before retrying on error
                await asyncio.sleep(30)

    async def _calculate_dynamic_wait_time(self) -> int:
        """
        Dynamically calculate wait time based on new messages or minimum interval.
        Returns:
            int: Wait time in seconds
        """
        try:
            # Default wait time (30 seconds for more responsive processing)
            default_wait_time = 30
            # Check for new messages across all active registers
            session = await self.db_service.get_session()
            try:
                from sqlalchemy import select, func
                # Get the most recent tg_content timestamp
                stmt = select(func.max(TgContent.created_at))
                result = await session.execute(stmt)
                latest_message_timestamp = result.scalar()
                if latest_message_timestamp:
                    # Check if we have a stored timestamp for comparison
                    current_time = int(time.time())
                    # If we haven't processed any messages yet, or if there are new messages
                    if not hasattr(self, '_last_check_timestamp'):
                        self._last_check_timestamp = current_time
                        return default_wait_time
                    # Check if there are new messages since our last check
                    if latest_message_timestamp > self._last_check_timestamp:
                        with langchain_processor_logger.begin_section("Dynamic Wait Time") as section:
                            section.log_step("New messages detected, processing immediately")
                        self._last_check_timestamp = current_time
                        return 1  # Process immediately
                    # Update our last check timestamp
                    self._last_check_timestamp = current_time
                # If no new messages, return default wait time
                return default_wait_time
            finally:
                await session.close()
        except Exception as e:
            with langchain_processor_logger.begin_section("Dynamic Wait Time") as section:
                section.log_step("Error calculating dynamic wait time")
                section.log_step(f"Error details - Error: {str(e)}")
            # Fall back to default wait time on error
            return 30

    async def _process_register_content(self, register: Register):
        """
        Process content for a specific register entry from tg_content table.
        Args:
            register: Register entry to process content for
        """
        try:
            # Create a single section for the entire method execution
            register_section = langchain_processor_logger.begin_section("Register Processing")
            section = register_section.__enter__()
            section.log_step(f"Processing content for register: {register.project_id}")
            
            # Step 0: Check if register should be skipped due to invalid configuration
            if self._should_skip_register_due_to_config(register):
                section.log_step("Skipping register due to invalid Telegram configuration")
                section.log_step(f"Project ID: {register.project_id}")
                section.log_completion("Register processing skipped - invalid configuration", "warning")
                register_section.__exit__(None, None, None)
                return
            
            # Step 1: Check crawling completion status
            section.log_step("Step 1: Checking crawling completion status for register")
            section.log_step(f"Project ID: {register.project_id}")
            # Check if crawling is completed before processing
            session = await self.db_service.get_session()
            try:
                crawling_completed = await check_crawling_completion_status(session, register.project_id)
            finally:
                await session.close()
            if not crawling_completed:
                section.log_step("Crawling not completed for register. Skipping processing.")
                section.log_step(f"Project ID: {register.project_id}")
                section.log_completion("Register processing skipped - waiting for crawling completion", "pending")
                # Close the section before returning
                register_section.__exit__(None, None, None)
                return
            # Step 2: Check if we should process based on last post time and new messages
            section.log_step("Step 2: Checking if register should be processed")
            section.log_step(f"Project ID: {register.project_id}")
            # Check if we should process based on last post time and new messages
            should_process = await self._should_process_register(register)
            if not should_process:
                section.log_step("Skipping processing for register - no new messages or not time to post")
                section.log_step(f"Project ID: {register.project_id}")
                section.log_completion("Register processing skipped - no new content to process", "skipped")
                # Close the section before returning
                register_section.__exit__(None, None, None)
                return
            # Step 3: Get unprocessed tg_content entries for this register
            section.log_step("Step 3: Retrieving unprocessed tg_content entries")
            section.log_step(f"Project ID: {register.project_id}")
            # Get unprocessed tg_content entries for this register
            session = await self.db_service.get_session()
            try:
                from sqlalchemy import select
                # Get tg_entities associated with this register
                register_entities = await get_register_tg_entities(session, register.project_id)
                entity_ids = [entity.id for entity in register_entities]
                if not entity_ids:
                    section.log_step("No entities found for register")
                    section.log_step(f"Project ID: {register.project_id}")
                    section.log_completion("Register processing failed - no entities configured", "failed")
                    # Close the section before returning
                    register_section.__exit__(None, None, None)
                    return
                # Get tg_content entries that haven't been processed yet and are newer than last post time
                stmt = select(TgContent).where(
                    TgContent.entities_id.in_(entity_ids)
                ).order_by(TgContent.created_at.desc()).limit(5)  # Reduced to 5 messages to avoid overload
                result = await session.execute(stmt)
                tg_contents = result.scalars().all()
                section.log_step(f"Found unprocessed tg_content entries for project: {len(tg_contents)}")
                # Filter tg_content based on last processed timestamp
                if register.project_id in self.last_processed_timestamps:
                    last_timestamp = self.last_processed_timestamps[register.project_id]
                    original_count = len(tg_contents)
                    tg_contents = [content for content in tg_contents if content.created_at > last_timestamp]
                    filtered_count = len(tg_contents)
                    section.log_step("Filtered to tg_content entries newer than last processed timestamp")
                    section.log_step(f"Filter details - Filtered count: {filtered_count}, Last timestamp: {last_timestamp}, Original count: {original_count}, Removed count: {original_count-filtered_count}")
                else:
                    section.log_step("No previous processing timestamp found for project, processing all entries")
                    section.log_step(f"Project ID: {register.project_id}")
                # Limit to 3 entries at a time to prevent overload
                original_count = len(tg_contents)
                tg_contents = tg_contents[:3]
                if original_count > 3:
                    section.log_step("Limited processing to 3 entries for project")
                    section.log_step(f"Limit details - Original count: {original_count}, Project ID: {register.project_id}")
                # Step 4: Process each tg_content entry with Langchain
                section.log_step("Step 4: Processing tg_content entries with LLM")
                section.log_step(f"Entry count - Count: {len(tg_contents)}, Project ID: {register.project_id}")
                # Process each tg_content entry with Langchain
                processed_messages = []
                for tg_content in tg_contents:
                    try:
                        # Check if this content has already been posted (duplicate check)
                        # Per user requirement: All messages should be stored as "telegram_post" in the database
                        # reply_telegram_post is only used when replying to user messages in tg_chat_id
                        message_type = "telegram_post"
                        is_unique = await is_message_unique(session, register.project_id, tg_content.content, message_type)
                        if not is_unique:
                            section.log_step("Skipping tg_content - content already posted for project")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Project ID: {register.project_id}")
                            continue
                        else:
                            section.log_step("tg_content is unique, proceeding with processing for project")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Project ID: {register.project_id}")
                        # Pre-check: Skip content based on length limits from config
                        min_length = self.config.get("application", {}).get("min_content_length", 5)
                        max_length = self.config.get("application", {}).get("max_content_length", 10000)
                        # Check if content is None or empty before processing
                        if not tg_content.content:
                            section.log_step("Skipping tg_content - content is None or empty")
                            section.log_step(f"Content details - Content ID: {tg_content.id}")
                            continue
                        content_length = len(tg_content.content.strip())
                        if content_length < min_length:
                            section.log_step("Skipping tg_content - content too short")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Content length: {content_length}, Min length: {min_length}")
                            continue
                        if content_length > max_length:
                            section.log_step("Skipping tg_content - content too long")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Content length: {content_length}, Max length: {max_length}")
                            continue
                        # Process tg_content with LLM
                        llm_result = await self._process_message_with_llm(register, tg_content.content, tg_content)
                        # Log LLM processing results
                        llm_success = llm_result and llm_result.get('success', False)
                        llm_queued = llm_result and llm_result.get('queued', False)

                        if llm_queued:
                            # Message was queued for later processing - skip adding to processed messages
                            section.log_step("Message queued for later processing")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Reply to: {tg_content.reply_to}")
                            continue  # Skip this message and move to next

                        if not llm_success:
                            error_msg = llm_result.get('error', 'Unknown error') if llm_result else 'No result'
                            section.log_step("LLM processing failed for tg_content")
                            section.log_step(f"Content details - Content ID: {tg_content.id}, Error: {error_msg}")
                        # Add LLM result to message data
                        # Get entity information for enhanced posting
                        entity_name = "Unknown Entity"
                        try:
                            # Find the entity name from the register entities
                            for entity in register_entities:
                                if entity.id == tg_content.entities_id:
                                    entity_name = entity.name
                                    break
                        except:
                            pass

                        processed_message = {
                            'id': tg_content.id,
                            'content': tg_content.content,
                            'llm_analysis': llm_result,
                            'entities_id': tg_content.entities_id,
                            'entity_id': tg_content.entities_id,  # For enhanced posting
                            'entity_name': entity_name,  # For enhanced posting
                            'message_id': tg_content.message_id,
                            'user_id': tg_content.user_id,
                            'created_at': tg_content.created_at,
                            'reply_to': tg_content.reply_to,  # Add reply information (only attribute that exists)
                            'processed_message': {  # Nested for database operations
                                'entity_id': tg_content.entities_id,
                                'message_id': tg_content.message_id,
                                'created_at': tg_content.created_at,
                                'reply_to': tg_content.reply_to
                            }
                        }
                        processed_messages.append(processed_message)

                        # Check if any pending replies were waiting for this message
                        waiting_replies = reply_queue_manager.check_parent_available(tg_content.entities_id, tg_content.message_id)
                        if waiting_replies:
                            section.log_step(f"Found {len(waiting_replies)} pending replies waiting for this message")
                            section.log_step(f"Parent message ID: {tg_content.message_id}, Waiting reply IDs: {waiting_replies}")

                        # Log a snippet of the content for debugging (first 100 characters)
                        content_snippet = tg_content.content[:100] + ('...' if len(tg_content.content) > 100 else '')
                        section.log_step("Added tg_content to processing queue")
                        section.log_step(f"Content snippet: {content_snippet}")
                        section.log_step("Processed tg_content with LLM")
                        section.log_step(f"Content details - Content ID: {tg_content.id}, Success: {llm_success}")
                        # Log additional details about LLM processing
                        if llm_result and 'response' in llm_result:
                            response_snippet = llm_result['response'][:100] + ('...' if len(llm_result['response']) > 100 else '')
                            section.log_step("LLM response for tg_content")
                            section.log_step(f"Response snippet: {response_snippet}")
                    except Exception as e:
                        section.log_step("Error processing tg_content with LLM")
                        section.log_step(f"Content details - Content ID: {tg_content.id}, Error: {str(e)}")
                        # Log the content that failed processing (first 100 characters)
                        content_snippet = tg_content.content[:100] + ('...' if len(tg_content.content) > 100 else '')
                        section.log_step("Failed content snippet")
                        section.log_step(f"Content snippet: {content_snippet}")
                # Update last processed timestamp
                # Only update based on successfully processed messages, not all tg_contents
                if processed_messages:
                    # Get the latest created_at timestamp from successfully processed messages
                    latest_message_time = max(msg['created_at'] for msg in processed_messages if 'created_at' in msg)
                    previous_timestamp = self.last_processed_timestamps.get(register.project_id, None)
                    self.last_processed_timestamps[register.project_id] = latest_message_time
                    section.log_step("Updated last processed timestamp for project")
                    section.log_step(f"Timestamp details - Project ID: {register.project_id}, Previous timestamp: {previous_timestamp}, Latest timestamp: {latest_message_time}")
                elif tg_contents:
                    # If we had tg_contents but none were processed successfully, 
                    # still update timestamp to avoid reprocessing the same failed messages
                    latest_message_time = max(content.created_at for content in tg_contents)
                    previous_timestamp = self.last_processed_timestamps.get(register.project_id, None)
                    self.last_processed_timestamps[register.project_id] = latest_message_time
                    section.log_step("Updated last processed timestamp for project (no successful processing)")
                    section.log_step(f"Timestamp details - Project ID: {register.project_id}, Previous timestamp: {previous_timestamp}, Latest timestamp: {latest_message_time}")
                # Log detailed information about processed messages
                section.log_step(f"Found unprocessed tg_content entries for project: {len(tg_contents)}")
                section.log_step("Successfully processed messages for project")
                section.log_step(f"Message count - Count: {len(processed_messages)}, Project ID: {register.project_id}")
                # Step 5: Post processed messages to Telegram
                section.log_step("Step 5: Posting processed messages to Telegram")
                section.log_step(f"Message count - Count: {len(processed_messages)}, Project ID: {register.project_id}")
                # Here you would typically post the processed content to Telegram
                # based on the register's tg_chat_id and other settings
                if processed_messages:
                    section.log_step("Posting processed messages to Telegram")
                    section.log_step(f"Message count: {len(processed_messages)}")
                    # Enhanced posting: Group messages by entity and combine them into consolidated posts
                    await self._post_to_telegram_enhanced(register, processed_messages)
                else:
                    reason_details = []
                    if not tg_contents:
                        reason_details.append("no unprocessed tg_content entries")
                    else:
                        reason_details.append(f"{len(tg_contents)} tg_content entries found but none passed processing")
                    # Log detailed reasons why no messages are being posted
                    if tg_contents:
                        skipped_reasons = []
                        for content in tg_contents:
                            # Try to determine why each content was skipped
                            content_snippet = content.content[:50] + ('...' if len(content.content) > 50 else '')
                            skipped_reasons.append(f"tg_content {content.id}: '{content_snippet}'")
                        if skipped_reasons:
                            section.log_step("Skipped content details for project")
                            section.log_step(f"Project ID: {register.project_id}, Skipped reasons: {', '.join(skipped_reasons)}")
                    section.log_step("No processed messages to post to Telegram for project")
                    section.log_step(f"Project ID: {register.project_id}, Reason: {', '.join(reason_details)}")

                # Step 6: Check for pending reply messages that can now be processed
                section.log_step("Step 6: Checking for pending reply messages")
                await self._process_pending_replies(register, entity_ids)

                # Final Step: Log completion status with tick indicator
                section.log_completion("Register processing completed successfully", "success", project_id=register.project_id)

            finally:
                await session.close()
            # Close the section before returning
            register_section.__exit__(None, None, None)
        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error processing content for register")
                section.log_step(f"Error details - Project ID: {register.project_id}, Error: {str(e)}")
                # Close the section before returning
                register_section.__exit__(None, None, None)
            else:
                # Create a section just to log the error
                with langchain_processor_logger.begin_section("Register Processing") as section:
                    section.log_step("Error processing content for register")
                    section.log_step(f"Error details - Project ID: {register.project_id}, Error: {str(e)}")

    async def _should_process_register(self, register: Register) -> bool:
        """
        Determine if a register should be processed based on last post time and new tg_content entries.
        Args:
            register: Register entry to check
        Returns:
            bool: True if register should be processed, False otherwise
        """
        try:
            # Create a single section for the entire method execution
            with langchain_processor_logger.begin_section("Register Processing") as section:
                # Step 1: Get last post time for this register
                section.log_step("Step 1: Getting last post time for register")
                section.log_step(f"Project ID: {register.project_id}")
                session = await self.db_service.get_session()
                try:
                    # Get last post time for this register
                    last_post_time = await get_last_post_time(session, register.project_id)
                    # Step 2: Get the latest tg_content entry for this register's entities
                    section.log_step("Step 2: Getting latest tg_content entry for register")
                    section.log_step(f"Project ID: {register.project_id}")
                    # Get the latest tg_content entry for this register's entities
                    from sqlalchemy import select, func
                    # Get tg_entities associated with this register
                    register_entities = await get_register_tg_entities(session, register.project_id)
                    entity_ids = [entity.id for entity in register_entities]
                    # Log entity information for debugging
                    entity_names = [entity.name for entity in register_entities]
                    section.log_step("Found entities for project")
                    section.log_step(f"Entity details - Project ID: {register.project_id}, Count: {len(entity_ids)}, Entity names: {', '.join(entity_names)}")
                    if not entity_ids:
                        section.log_step("No entities found for project")
                        section.log_step(f"Project ID: {register.project_id}")
                        return False
                    stmt = select(func.max(TgContent.created_at)).where(
                        TgContent.entities_id.in_(entity_ids)
                    )
                    result = await session.execute(stmt)
                    latest_content_time = result.scalar()
                    # Step 3: Check if there are tg_content entries
                    section.log_step("Step 3: Checking if tg_content entries exist")
                    section.log_step(f"Project ID: {register.project_id}")
                    # If there are no tg_content entries, don't process
                    if not latest_content_time:
                        section.log_step("No tg_content entries found for project")
                        section.log_step(f"Project ID: {register.project_id}")
                        return False
                    # Step 4: Check if there's a last post time
                    section.log_step("Step 4: Checking last post time")
                    section.log_step(f"Project ID: {register.project_id}")
                    # If there's no last post time, process (first time)
                    if not last_post_time:
                        section.log_step("No last post time found for project, processing all available content")
                        section.log_step(f"Project ID: {register.project_id}")
                        # Log how many content entries are available
                        count_stmt = select(func.count(TgContent.id)).where(
                            TgContent.entities_id.in_(entity_ids)
                        )
                        count_result = await session.execute(count_stmt)
                        content_count = count_result.scalar()
                        section.log_step("Found total tg_content entries for project")
                        section.log_step(f"Count - Count: {content_count}, Project ID: {register.project_id}")
                        return True
                    # Step 5: Compare timestamps
                    section.log_step("Step 5: Comparing timestamps")
                    section.log_step(f"Project ID: {register.project_id}")
                    # Log the comparison details
                    section.log_step("Comparing timestamps for project")
                    section.log_step(f"Timestamp details - Project ID: {register.project_id}, Latest content time: {latest_content_time}, Last post time: {last_post_time}")

                    # Process if there are new tg_content entries since last post
                    if latest_content_time > last_post_time:
                        section.log_step("New tg_content entries found for project")
                        return True

                    # Don't process if no new tg_content entries
                    section.log_step("No new tg_content entries for project")
                    return False
                finally:
                    await session.close()
        except Exception as e:
            with langchain_processor_logger.begin_section("Register Processing") as section:
                section.log_step("Error checking if register should be processed")
                section.log_step(f"Project ID: {register.project_id}, Error: {str(e)}")
            return False

    async def _validate_telegram_configuration(self):
        """
        Validate Telegram bot token and chat ID configuration for all active registers.
        This method checks configuration validity and logs diagnostic information.
        """
        try:
            with langchain_processor_logger.begin_section("Configuration Validation") as section:
                section.log_step("Starting Telegram configuration validation")
                
                # Get all active registers
                session = await self.db_service.get_session()
                try:
                    from sqlalchemy import select
                    stmt = select(Register).where(Register.active == 1)
                    result = await session.execute(stmt)
                    active_registers = result.scalars().all()
                    
                    section.log_step(f"Found active registers to validate: {len(active_registers)}")
                    
                    valid_count = 0
                    invalid_count = 0
                    
                    for register in active_registers:
                        validation_result = await self._validate_register_telegram_config(register)
                        
                        if validation_result['is_valid']:
                            valid_count += 1
                            section.log_step(f"Register {register.project_id}: Configuration valid")
                        else:
                            invalid_count += 1
                            self.invalid_registers.add(register.project_id)
                            section.log_step(f"Register {register.project_id}: Configuration invalid")
                            section.log_step(f"Validation errors: {', '.join(validation_result['errors'])}")
                    
                    # Set overall validation state
                    self.config_validation_passed = invalid_count == 0
                    
                    section.log_step(f"Configuration validation complete - Valid: {valid_count}, Invalid: {invalid_count}")
                    
                    if invalid_count > 0:
                        section.log_step("Service will continue with graceful degradation for invalid registers")
                    else:
                        section.log_step("All register configurations are valid")
                        
                finally:
                    await session.close()
                    
        except Exception as e:
            with langchain_processor_logger.begin_section("Configuration Validation") as section:
                section.log_step("Error during configuration validation")
                section.log_step(f"Error details: {str(e)}")
            # Set validation as failed but allow service to continue
            self.config_validation_passed = False

    async def _validate_register_telegram_config(self, register: Register) -> Dict[str, Any]:
        """
        Validate Telegram configuration for a specific register.
        
        Args:
            register: Register to validate
            
        Returns:
            Dict containing validation results
        """
        errors = []
        warnings = []
        
        try:
            # Check bot token
            if not register.tg_bot_token or not register.tg_bot_token.strip():
                errors.append("Missing Telegram bot token")
            elif not self._is_valid_bot_token_format(register.tg_bot_token):
                errors.append("Invalid Telegram bot token format")
            
            # Check chat ID
            if not register.tg_chat_id or not register.tg_chat_id.strip():
                errors.append("Missing Telegram chat ID")
            elif not self._is_valid_chat_id_format(register.tg_chat_id):
                warnings.append("Chat ID format may be invalid")
            
            # Check optional message thread ID
            if register.message_thread_id is not None and register.message_thread_id <= 0:
                warnings.append("Message thread ID should be positive if specified")
            
            # Additional validation: Check if bot token and chat ID combination makes sense
            if register.tg_bot_token and register.tg_chat_id:
                # Perform basic API validation if enabled in config
                api_validation_enabled = self.config.get("telegram", {}).get("validate_config_on_startup", False)
                if api_validation_enabled:
                    api_valid = await self._test_telegram_api_connection(register.tg_bot_token, register.tg_chat_id)
                    if not api_valid:
                        errors.append("Telegram API connection test failed")
            
            return {
                'is_valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'project_id': register.project_id
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': [],
                'project_id': register.project_id
            }

    def _is_valid_bot_token_format(self, token: str) -> bool:
        """
        Check if bot token has valid format.
        Telegram bot tokens follow pattern: <bot_id>:<auth_token>
        """
        if not token:
            return False
        
        # Basic format check: should contain colon and have reasonable length
        parts = token.split(':')
        if len(parts) != 2:
            return False
        
        bot_id, auth_token = parts
        
        # Bot ID should be numeric
        if not bot_id.isdigit():
            return False
        
        # Auth token should be alphanumeric and have reasonable length
        if len(auth_token) < 20 or not auth_token.replace('_', '').replace('-', '').isalnum():
            return False
        
        return True

    def _is_valid_chat_id_format(self, chat_id: str) -> bool:
        """
        Check if chat ID has valid format.
        Chat IDs can be numeric (positive for users, negative for groups/channels) or @username
        """
        if not chat_id:
            return False
        
        chat_id = chat_id.strip()
        
        # Username format
        if chat_id.startswith('@'):
            username = chat_id[1:]
            if len(username) == 0:
                return False
            # Username can contain letters, numbers, underscores, and dashes
            return username.replace('_', '').replace('-', '').isalnum()
        
        # Numeric format
        try:
            int(chat_id)
            return True
        except ValueError:
            return False

    async def _test_telegram_api_connection(self, bot_token: str, chat_id: str) -> bool:
        """
        Test Telegram API connection with the given bot token and chat ID.
        This is an optional validation that can be enabled in config.
        """
        try:
            # Use getMe API to test bot token validity
            url = f"https://api.telegram.org/bot{bot_token}/getMe"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('ok', False)
                    else:
                        return False
                        
        except Exception:
            # If API test fails, don't block the service - just return False
            return False

    def _should_skip_register_due_to_config(self, register: Register) -> bool:
        """
        Check if a register should be skipped due to invalid configuration.
        
        Args:
            register: Register to check
            
        Returns:
            bool: True if register should be skipped, False otherwise
        """
        return register.project_id in self.invalid_registers

    async def _process_message_with_llm(self, register: Register, message_content: str, message: TgContent = None) -> Dict[str, Any]:
        """
        Process a message using the LLM service with enhanced features:
        1. Push max token to maximum 50%
        2. State if message is from history data only
        3. If message is a reply, include the original message
        4. Enhanced logic to determine if original message should be processed instead of reply
        Args:
            register: Register data containing LLM configuration
            message_content: Content of the message to process
            message: Optional TgContent object for context (includes reply information)
        Returns:
            Dict[str, Any]: Processing result
        """
        llm_section = None  # Initialize to None to handle early exceptions
        try:
            # Step 1: Starting processing
            # Create a single section for the entire method execution
            llm_section = langchain_processor_logger.begin_section("LLM Processing")
            section = llm_section.__enter__()  # FIXED: Changed 'lll_section' to 'llm_section'
            section.log_step("Step 1: Starting LLM processing for content")
            section.log_step(f"Message details - Message ID: {message.id if message else None}")
            # Log the beginning of LLM processing in JSON format
            content_snippet = message_content[:50] + ('...' if len(message_content) > 50 else '')
            # Enhanced Implementation: Replace with structured JSON logging
            section.log_step("Starting LLM processing for content")
            section.log_step("Message data: ")
            # Convert register object to dictionary for LLM service
            register_data = {
                'phone_number': register.phone_number,
                'model_selection': register.model_selection,
                'llm_api_key': register.llm_api_key,
                'llm_max_token': int(register.llm_max_token * 1.5),  # Increase max token by 50%
                'llm_temperature': register.llm_temperature,
                'llm_top_p': register.llm_top_p,
                'llm_system_prompt': register.llm_system_prompt,
                'llm_user_prompt': register.llm_user_prompt,
                'keyword': register.keyword,
                'hashtag': register.hashtag
            }
            # Step 2: Context preparation
            section.log_step("Step 2: Preparing context for LLM processing")
            section.log_step(f"Reply status - Is reply: {bool(message and message.reply_to)}")
            # Prepare context for LLM processing
            context = {}
            # Add date information to indicate if this is historical data
            from datetime import datetime
            context['processing_timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # Check if this might be historical data based on message age
            # For tg_content, we can use created_at directly
            if message is not None and hasattr(message, 'created_at') and message.created_at:
                try:
                    # Convert message.created_at to timestamp if it's a datetime object
                    if hasattr(message.created_at, 'timestamp'):
                        message_timestamp = message.created_at.timestamp()
                    else:
                        # Assume it's already a timestamp
                        message_timestamp = float(message.created_at)

                    message_age_days = (time.time() - message_timestamp) / (24 * 3600)
                    if message_age_days > 7:  # If message is older than a week, consider it historical
                        context['date_context'] = f"This message is from historical data ({message_age_days:.1f} days ago)"
                    else:
                        context['date_context'] = f"This is recent data (posted {message_age_days:.1f} days ago)"
                    section.log_step("Message age")
                    section.log_step(f"Age details - Days: {message_age_days}, Context: {context['date_context']}")
                except Exception as e:
                    # If there's any issue with date calculation, just skip it
                    section.log_step(f"Could not calculate message age: {str(e)}")
                    context['date_context'] = "Message age could not be determined"
            # Enhanced reply message handling - Queue-first approach
            # Check if this is a reply message BEFORE any processing
            is_reply_message = message is not None and hasattr(message, 'reply_to') and message.reply_to

            if is_reply_message:
                # This is a reply message - check if we should queue it or process it
                section.log_step("Detected reply message - checking parent availability")
                section.log_step(f"Reply details - Reply to: {message.reply_to}, Message ID: {message.message_id}")

                # First, check if parent message exists in database
                session = await self.db_service.get_session()
                try:
                    from sqlalchemy import select
                    stmt = select(TgContent).where(
                        TgContent.entities_id == message.entities_id,
                        TgContent.message_id == message.reply_to
                    )
                    result = await session.execute(stmt)
                    parent_message = result.scalars().first()

                    if not parent_message:
                        # Parent not available - add to queue and skip processing for now
                        section.log_step("Parent message not available - adding to reply queue")
                        section.log_step(f"Queue details - Reply message ID: {message.message_id}, Parent message ID: {message.reply_to}")
                        reply_queue_manager.add_pending_reply(message, message.reply_to)

                        # Return a special result indicating this message was queued
                        queued_result = {
                            'success': True,
                            'queued': True,
                            'response': 'Reply message queued for later processing when parent becomes available',
                            'json_data': None,
                            'token_count': 0,
                            'is_crypto_related': False,
                            'reply_context': f"Reply to message {message.reply_to} - queued for later processing"
                        }
                        return queued_result

                finally:
                    await session.close()

                # Parent exists - proceed with reply processing
                section.log_step("Parent message found - proceeding with reply processing")
                try:
                    # Step 3: Reply chain traversal (parent already found above)
                    section.log_step("Step 3: Traversing reply chain")
                    section.log_step("Traversal details - Traversal depth: 0")  # Will be updated during traversal
                    # Traverse up the reply chain to find the very top original message
                    # Keep going up until we find a message where reply_to is null
                    session = await self.db_service.get_session()
                    try:
                        # We already have the immediate parent from the check above
                        # Start traversal from the parent we found
                        # Keep traversing up the chain until we find the original message (reply_to is null)
                        original_message = parent_message
                        traversal_count = 0
                        max_traversal = 10  # Prevent infinite loops
                        while (original_message and 
                               hasattr(original_message, 'reply_to') and 
                               original_message.reply_to and 
                               traversal_count < max_traversal):
                            section.log_step("Traversing up reply chain")
                            section.log_step(f"Traversal details - Current message ID: {original_message.message_id}, Reply to: {original_message.reply_to}, Traversal count: {traversal_count}")
                            # Get the next parent in the chain
                            stmt = select(TgContent).where(
                                TgContent.entities_id == original_message.entities_id,
                                TgContent.message_id == original_message.reply_to
                            )
                            result = await session.execute(stmt)
                            next_parent = result.scalars().first()
                            # If we found a parent, continue traversing
                            if next_parent:
                                original_message = next_parent
                                traversal_count += 1
                            else:
                                # No more parents found, break the loop
                                break
                        # Log traversal results
                        if traversal_count > 0:
                            section.log_step("Completed reply chain traversal")
                            section.log_step(f"Traversal results - Total hops: {traversal_count}, Final original message ID: {original_message.message_id if original_message else None}")
                        if original_message:
                            context['is_reply'] = True
                            context['original_message'] = original_message.content
                            context['reply_context'] = f"This message is a reply in a chain, original message: {original_message.content[:100]}..."
                            original_snippet = original_message.content[:50] + ('...' if len(original_message.content) > 50 else '')
                            section.log_step("Found top-level original message for reply chain")
                            section.log_step(f"Content snippet: {original_snippet}")
                            # Store the original message content to include in the final post
                            context['original_message_content'] = original_message.content
                            # Enhanced logic: Determine if we should process the original message instead
                            # This is useful when the reply is just a simple acknowledgment or short response
                            should_process_original = self._should_process_original_message(message_content, original_message.content)
                            if should_process_original:
                                section.log_step("Switching to process original message instead of reply")
                                context['processing_original_message'] = True
                                # Update the message content to the original message
                                message_content = original_message.content
                                # Update context to indicate we're processing the original
                                context['processed_message_type'] = "original"
                                context['reply_message_content'] = message.content  # Store the reply content for reference
                            else:
                                context['processed_message_type'] = "reply"
                                # Build complete reply chain: original message + all replies until latest
                                reply_chain_content = await self._build_complete_reply_chain(session, original_message, message)
                                message_content = reply_chain_content
                                # Store the complete reply chain for Telegram posting
                                context['complete_reply_chain'] = reply_chain_content
                        else:
                            # This should never happen since we already checked parent availability above
                            # If we reach here, there's a logic error - log it and treat as error
                            section.log_step("ERROR: Parent message not found in reply chain traversal - this should not happen")
                            section.log_step(f"Error details - Reply message ID: {message.message_id}, Parent message ID: {message.reply_to}")
                            context['is_reply'] = True
                            context['original_message'] = None
                            context['reply_context'] = "This message is a reply, but encountered an unexpected error during reply chain traversal."
                    finally:
                        await session.close()
                except Exception as e:
                    section.log_step("Error traversing reply chain")
                    section.log_step(f"Message details - Message ID: {getattr(message, 'id', 'unknown') if message else 'unknown'}, Error: {str(e)}")
                    context['is_reply'] = True
                    context['original_message'] = None
                    context['reply_context'] = "This message is a reply, but encountered an error retrieving the original message."
            else:
                # This is NOT a reply message - process it separately and immediately
                section.log_step("Processing non-reply message")
                section.log_step(f"Message details - Message ID: {getattr(message, 'message_id', 'unknown') if message else 'unknown'}")
                context['is_reply'] = False
                context['processed_message_type'] = "standalone"
                # No reply context needed for standalone messages

            # Step 4: LLM processing
            section.log_step("Step 4: Calling LLM service with prepared context")
            # Process message with LLM service, passing the enhanced context
            section.log_step("Calling LLM service with prepared context")
            result = await self.llm_service.process_message_with_llm(register_data, message_content, context)
            # Step 5: Processing result
            section.log_step("Step 5: LLM processing completed")
            section.log_step(f"Success status - Success: {result.get('success', False)}")
            # Log the result
            if result and result.get('success'):
                response_snippet = result.get('response', '')[:100] + ('...' if len(result.get('response', '')) > 100 else '')
                section.log_step("LLM processing successful")
                section.log_step(f"Response snippet: {response_snippet}")
            else:
                error_msg = result.get('error', 'Unknown error') if result else 'No result returned'
                section.log_step("LLM processing failed")
                section.log_step(f"Error details - Error: {error_msg}")
            # Add original message content to the result if it was a reply
            if context.get('original_message_content'):
                result['original_message_content'] = context['original_message_content']
            # Add complete reply chain to the result if it was a reply
            if context.get('complete_reply_chain'):
                result['complete_reply_chain'] = context['complete_reply_chain']
            # Close the section before returning
            if llm_section is not None:
                llm_section.__exit__(None, None, None)
            return result
        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error processing message with LLM for user")
                section.log_step(f"User details - Phone number: {register.phone_number}, Error: {str(e)}")
            # Close the section before returning (only if it was created)
            if llm_section is not None:
                llm_section.__exit__(None, None, None)
            return {
                'success': False,
                'error': str(e),
                'response': None,
                'json_data': None,
                'token_count': 0,
                'is_crypto_related': False
            }

    def _should_process_original_message(self, reply_content: str, original_content: str) -> bool:
        """
        Determine if the original message should be processed instead of the reply.
        This is useful when the reply is just a simple acknowledgment or short response.
        Args:
            reply_content: Content of the reply message
            original_content: Content of the original message
        Returns:
            bool: True if original message should be processed, False otherwise
        """
        try:
            # Define simple reply patterns that indicate the reply doesn't add significant value
            simple_reply_patterns = [
                r'^\s*(yes|no|yep|nope|yeah|nah|ok|okay|thanks|thank you|thx|sure|got it|understood|understand|gotcha|roger|affirmative|negative)\s*[\.!\?]?$',
                r'^\s*(up|down|buy|sell|long|short)\s*[\.!\?]?$',
                r'^\s*\+1\s*$',
                r'^\s*👍\s*$',
                r'^\s*agree\s*[\.!\?]?$',
                r'^\s*disagree\s*[\.!\?]?$',
                r'^\s*same\s*[\.!\?]?$',
                r'^\s*ditto\s*[\.!\?]?$',
                r'^\s*copy\s*[\.!\?]?$',
                r'^\s*paste\s*[\.!\?]?$'
            ]
            reply_content_lower = reply_content.lower().strip()
            # Check if reply is a simple acknowledgment
            for pattern in simple_reply_patterns:
                if re.match(pattern, reply_content_lower, re.IGNORECASE):
                    return True
            # Check if reply is significantly shorter than original (less than 25% of original length)
            if len(reply_content.strip()) < len(original_content.strip()) * 0.25 and len(reply_content.strip()) < 100:
                return True
            # Check if reply is just an emoji or a few emojis
            emoji_pattern = r'^[\s\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002500-\U00002BEF\U00002702-\U000027B0\U00002702-\U000027B0\U000024C2-\U0001F251\U0001f926-\U0001f937\U00010000-\U0010ffff]+[\s\.!\?]*$'
            if re.match(emoji_pattern, reply_content.strip()):
                return True
            # If none of the above conditions are met, process the reply as is
            return False
        except Exception as e:
            # Default to processing the reply if there's an error in determination
            return False

    async def _build_complete_reply_chain(self, session, original_message, current_message):
        """
        Build complete reply chain: original message + all replies until the current message.
        This traverses down from the original message to collect all replies in chronological order.
        """
        try:
            from sqlalchemy import select

            # Start with the original message
            reply_chain_parts = [original_message.content]

            # Get all messages that are replies in this chain, ordered by message_id (chronological)
            stmt = select(TgContent).where(
                TgContent.entities_id == original_message.entities_id,
                TgContent.message_id > original_message.message_id,
                TgContent.message_id <= current_message.message_id
            ).order_by(TgContent.message_id)

            result = await session.execute(stmt)
            all_messages = result.scalars().all()

            # Build a map of message_id to message for quick lookup
            message_map = {msg.message_id: msg for msg in all_messages}

            # Traverse the reply chain by following reply_to relationships
            def collect_replies_in_chain(start_msg_id, target_msg_id):
                """Recursively collect all replies in the chain leading to target_msg_id"""
                replies = []

                # Find all messages that reply to start_msg_id
                for msg in all_messages:
                    if msg.reply_to == start_msg_id:
                        replies.append(msg)
                        # If this is our target message, we're done with this branch
                        if msg.message_id == target_msg_id:
                            return [msg]
                        # Otherwise, continue down this branch
                        sub_replies = collect_replies_in_chain(msg.message_id, target_msg_id)
                        if sub_replies:  # If we found the target in this branch
                            return [msg] + sub_replies

                return []

            # Collect the reply chain leading to the current message
            reply_messages = collect_replies_in_chain(original_message.message_id, current_message.message_id)

            # Add all reply contents to the chain
            for reply_msg in reply_messages:
                reply_chain_parts.append(reply_msg.content)

            # Join all parts with newlines
            complete_chain = '\n'.join(reply_chain_parts)

            return complete_chain

        except Exception as e:
            # Fallback to simple original + current reply if chain building fails
            return f"{original_message.content}\n{current_message.content}"

    async def _should_combine_entities(self, register: Register, entity_groups: Dict) -> bool:
        """
        Determine if multiple entities should be combined into a single post.
        Args:
            register: Register entry
            entity_groups: Dictionary of entity groups
        Returns:
            bool: True if entities should be combined, False otherwise
        """
        try:
            # Combine if multiple entities exist
            if len(entity_groups) <= 1:
                return False

            # Check total message count - don't combine if too many messages
            total_messages = sum(len(group['messages']) for group in entity_groups.values())
            if total_messages > 10:  # Limit to prevent overly long posts
                return False

            # Check total content length - don't combine if too long
            total_length = 0
            for group in entity_groups.values():
                for message in group['messages']:
                    content = message.get('content', '')
                    llm_analysis = message.get('llm_analysis', {})
                    llm_response = llm_analysis.get('response', '')
                    total_length += len(content) + len(llm_response)

            # Telegram message limit is 4096 characters, leave room for formatting
            if total_length > 3000:
                return False

            # Default: combine multiple entities
            return True

        except Exception as e:
            # Default to not combining on error
            return False

    async def _post_to_telegram_enhanced(self, register: Register, processed_messages: List[Dict]):
        """
        Enhanced posting method that groups messages by entity and combines them into consolidated posts.
        This handles the one-to-many relationship between register and tg_entities.
        Args:
            register: Register entry with Telegram configuration
            processed_messages: List of processed messages with LLM analysis
        """
        try:
            # Group messages by entity
            entity_groups = {}
            for message in processed_messages:
                entity_id = message.get('entity_id')
                entity_name = message.get('entity_name', f'Entity_{entity_id}')

                if entity_id not in entity_groups:
                    entity_groups[entity_id] = {
                        'entity_name': entity_name,
                        'messages': []
                    }
                entity_groups[entity_id]['messages'].append(message)

            # Check if we should combine all entities into one post or post separately
            combine_all_entities = await self._should_combine_entities(register, entity_groups)

            if combine_all_entities:
                # Combine all entities into a single consolidated post
                await self._post_combined_entities(register, entity_groups)
            else:
                # Single entity - use original posting method
                await self._post_to_telegram(register, processed_messages)

        except Exception as e:
            with langchain_processor_logger.begin_section("Enhanced Telegram Posting") as section:
                section.log_step("Error in enhanced Telegram posting")
                section.log_step(f"Error details - Error: {str(e)}")
                # Fallback to original posting method
                await self._post_to_telegram(register, processed_messages)

    async def _post_combined_entities(self, register: Register, entity_groups: Dict):
        """
        Post combined content from multiple entities into a single Telegram message.
        Args:
            register: Register entry with Telegram configuration
            entity_groups: Dictionary of entity_id -> {entity_name, messages}
        """
        try:
            with langchain_processor_logger.begin_section("Combined Entity Posting") as section:
                section.log_step(f"Combining content from {len(entity_groups)} entities")

                # Build the combined message
                combined_parts = []
                all_messages_for_db = []

                for entity_id, group_data in entity_groups.items():
                    entity_name = group_data['entity_name']
                    messages = group_data['messages']

                    # Add simple entity header
                    combined_parts.append(f"**{entity_name}**")

                    # Add all messages from this entity
                    for i, message in enumerate(messages, 1):
                        content = message.get('content', '')
                        llm_analysis = message.get('llm_analysis', {})

                        # Format message based on type (reply vs non-reply)
                        if message.get('reply_to'):
                            # For reply messages: more formal and precise format
                            complete_reply_chain = llm_analysis.get('complete_reply_chain', '')
                            if complete_reply_chain:
                                # Extract key information from reply chain
                                formatted_message = self._format_reply_message_formal(complete_reply_chain, llm_analysis)
                            else:
                                formatted_message = self._format_reply_message_formal(content, llm_analysis)
                        else:
                            # For regular messages: simple format
                            message_content = content
                            llm_response = llm_analysis.get('response', '')
                            if llm_response:
                                formatted_message = f"{message_content}\n\n{llm_response}"
                            else:
                                formatted_message = message_content

                        # Add message with simple numbering for multiple messages
                        if len(messages) > 1:
                            combined_parts.append(f"{i}. {formatted_message}")
                        else:
                            combined_parts.append(formatted_message)

                        # Collect for database storage
                        all_messages_for_db.append(message)

                    combined_parts.append("")  # Space between entities

                # Create the final combined message
                combined_message = "\n".join(combined_parts).strip()

                # Post to Telegram
                await self._send_combined_telegram_message(register, combined_message, all_messages_for_db)

        except Exception as e:
            section.log_step("Error in combined entity posting")
            section.log_step(f"Error details - Error: {str(e)}")
            raise

    def _format_reply_message_formal(self, reply_content: str, llm_analysis: Dict) -> str:
        """
        Format reply messages in a formal, precise manner.
        Args:
            reply_content: The complete reply chain content
            llm_analysis: LLM analysis data
        Returns:
            str: Formatted reply message
        """
        try:
            # Extract the main discussion point from reply chain
            lines = reply_content.split('\n')

            # Find the latest reply (usually the last meaningful line)
            latest_reply = ""
            for line in reversed(lines):
                line = line.strip()
                if line and not line.startswith(('Original:', 'Reply:', 'Latest Reply:')):
                    latest_reply = line
                    break

            # If no clear latest reply found, use the last non-empty line
            if not latest_reply:
                for line in reversed(lines):
                    if line.strip():
                        latest_reply = line.strip()
                        break

            # Get LLM analysis
            llm_response = llm_analysis.get('response', '')

            # Format in a formal, concise manner
            if llm_response:
                # Combine the key reply point with analysis
                formatted = f"Discussion: {latest_reply}\n\nAnalysis: {llm_response}"
            else:
                # Just the key discussion point
                formatted = f"Discussion: {latest_reply}"

            return formatted

        except Exception as e:
            # Fallback to original content if formatting fails
            return reply_content

    async def _send_combined_telegram_message(self, register: Register, combined_message: str, all_messages: List[Dict]):
        """
        Send the combined message to Telegram and update database records.
        Args:
            register: Register entry with Telegram configuration
            combined_message: The combined message content
            all_messages: List of all messages included in the combined post
        """
        try:
            # Check if we have the required Telegram configuration
            if not register.tg_bot_token or not register.tg_chat_id:
                with langchain_processor_logger.begin_section("Combined Telegram Posting") as section:
                    section.log_step("Missing Telegram configuration for combined posting")
                    section.log_step(f"Project ID: {register.project_id}")
                return

            # Post to Telegram
            telegram_url = f"https://api.telegram.org/bot{register.tg_bot_token}/sendMessage"

            # Prepare the payload
            payload = {
                'chat_id': register.tg_chat_id,
                'text': combined_message,
                'parse_mode': 'Markdown'
            }

            # Add message thread ID if specified
            if register.message_thread_id:
                payload['message_thread_id'] = register.message_thread_id

            # Send the message
            async with aiohttp.ClientSession() as session:
                async with session.post(telegram_url, json=payload) as response:
                    if response.status == 200:
                        with langchain_processor_logger.begin_section("Combined Telegram Posting") as section:
                            section.log_step("Successfully posted combined message to Telegram")
                            section.log_step(f"Combined {len(all_messages)} messages from {len(set(msg.get('entity_id') for msg in all_messages))} entities")

                        # Update database records for all included messages
                        await self._update_combined_message_records(register, all_messages, combined_message)
                    else:
                        error_text = await response.text()
                        with langchain_processor_logger.begin_section("Combined Telegram Posting") as section:
                            section.log_step("Failed to post combined message to Telegram")
                            section.log_step(f"HTTP Status: {response.status}")
                            section.log_step(f"Error: {error_text}")

        except Exception as e:
            with langchain_processor_logger.begin_section("Combined Telegram Posting") as section:
                section.log_step("Error sending combined Telegram message")
                section.log_step(f"Error details - Error: {str(e)}")
                raise

    async def _update_combined_message_records(self, register: Register, all_messages: List[Dict], combined_message: str):
        """
        Update database records for all messages included in the combined post.
        Args:
            register: Register entry
            all_messages: List of all messages included in the combined post
            combined_message: The combined message content that was posted
        """
        try:
            db_session = await self.db_service.get_session()
            try:
                # Store each message in the database with the combined content
                successfully_posted_messages = []

                for message in all_messages:
                    processed_message = message.get('processed_message', {})
                    llm_analysis = message.get('llm_analysis', {})
                    content = message.get('content', '')

                    # Determine message_used content (as per your requirements)
                    message_used_content = content  # Default to original content
                    if processed_message.get('reply_to'):
                        complete_reply_chain = llm_analysis.get('complete_reply_chain', '')
                        if complete_reply_chain:
                            message_used_content = complete_reply_chain

                    # Create message record
                    message_record = Message(
                        project_id=register.project_id,
                        entity_id=processed_message.get('entity_id'),
                        message_id=processed_message.get('message_id'),
                        content=combined_message,  # Store the combined message
                        message_used=message_used_content,  # Store individual content as requested
                        message_type="telegram_post",
                        created_at=processed_message.get('created_at'),
                        posted_at=datetime.utcnow()
                    )

                    db_session.add(message_record)
                    successfully_posted_messages.append(processed_message)

                await db_session.commit()

                # Update last post time with the maximum timestamp
                if successfully_posted_messages:
                    max_posted_timestamp = max(msg.get('created_at', 0) for msg in successfully_posted_messages)
                    await update_last_post_time(db_session, register.project_id, max_posted_timestamp)

                    with langchain_processor_logger.begin_section("Combined Message Database") as section:
                        section.log_step(f"Successfully stored {len(successfully_posted_messages)} combined messages")
                        section.log_step(f"Updated last post time to: {max_posted_timestamp}")

            finally:
                await db_session.close()

        except Exception as e:
            with langchain_processor_logger.begin_section("Combined Message Database") as section:
                section.log_step("Error updating combined message records")
                section.log_step(f"Error details - Error: {str(e)}")
                raise

    async def _post_to_telegram(self, register: Register, processed_messages: List[Dict]):
        """
        Post processed content to Telegram with support for message thread IDs and date filtering.
        Args:
            register: Register entry with Telegram configuration
            processed_messages: List of processed messages with LLM analysis
        """
        try:
            # Check if we have the required Telegram configuration
            if not register.tg_chat_id or not register.tg_bot_token:
                # Create a single section for the entire method execution
                telegram_section = langchain_processor_logger.begin_section("Telegram Posting")
                section = telegram_section.__enter__()
                section.log_step("Skipping Telegram posting for project - missing tg_chat_id or tg_bot_token")
                section.log_step(f"Project ID: {register.project_id}")
                # Close the section before returning
                telegram_section.__exit__(None, None, None)
                return
            # Create a single section for the entire method execution
            telegram_section = langchain_processor_logger.begin_section("Telegram Posting")
            section = telegram_section.__enter__()
            section.log_step("Posting messages to Telegram for project")
            section.log_step(f"Message count - Message count: {len(processed_messages)}, Project ID: {register.project_id}")
            # Actually send messages to Telegram using HTTP API for bots
            import aiohttp
            try:
                # Use Telegram Bot API to send messages
                bot_token = register.tg_bot_token
                chat_id = register.tg_chat_id
                message_thread_id = register.message_thread_id  # Get message thread ID from register
                # Get database session to fetch message thread IDs for specific entities
                db_session = await self.db_service.get_session()
                try:
                    # Get register-tg_entity relationships with message_thread_id
                    from helpers.register_helpers import get_register_tg_entities_with_thread_info
                    entities_with_thread_info = await get_register_tg_entities_with_thread_info(db_session, register.project_id)
                finally:
                    await db_session.close()
                async with aiohttp.ClientSession() as session:
                    # Track the maximum timestamp of successfully posted messages
                    max_posted_timestamp = None
                    successfully_posted_messages = []

                    for processed_message in processed_messages:
                        content = processed_message.get('content', '')
                        llm_analysis = processed_message.get('llm_analysis', {})
                        # Check if content analysis was successful
                        llm_success = llm_analysis and llm_analysis.get('success', False)
                        if not llm_success:
                            section.log_step("LLM analysis failed for message - evaluating original content with fallback criteria")
                            section.log_step(f"Message details - Message ID: {processed_message.get('id')}")
                        # Check if the LLM response is meaningful (not just placeholder content)
                        response = llm_analysis.get('response', '') if llm_analysis else ''
                        # Check for various placeholder patterns
                        placeholder_indicators = [
                            'placeholder',
                            'XXX',
                            'Note: This is a placeholder',
                            'did not provide specific messages',
                            'generic summary',
                            'no specific information',
                            'no meaningful content'
                        ]
                        # Safely check for placeholder content, handling None responses
                        is_placeholder = False
                        if response:
                            is_placeholder = any(indicator.lower() in response.lower() for indicator in placeholder_indicators)
                        # Get configurable minimum response length from config
                        min_response_length = self.config.get("application", {}).get("validation_thresholds", {}).get("min_response_length", 20)
                        has_sufficient_content = len(response.strip()) > min_response_length
                        # Check if debug mode or bypass validation is enabled
                        debug_mode = self.config.get("application", {}).get("debug_mode", False)
                        bypass_validation = self.config.get("application", {}).get("bypass_content_validation", False)
                        
                        # Apply content quality filtering based on LLM success status
                        # If LLM analysis was successful, apply strict quality checks
                        # If LLM analysis failed, evaluate original content with fallback criteria
                        should_skip = False
                        
                        # Skip validation if debug mode or bypass is enabled
                        if debug_mode or bypass_validation:
                            should_skip = False
                            if debug_mode:
                                section.log_step("Debug mode enabled - bypassing content quality checks")
                                section.log_step(f"Debug details - LLM success: {llm_success}, Response length: {len(response.strip()) if response else 0}, Content length: {len(content.strip()) if content else 0}")
                        else:
                            if llm_success:
                                # For successful LLM analysis, apply strict quality checks
                                if not response or is_placeholder or (not has_sufficient_content and len(response.strip()) < 10):
                                    should_skip = True
                            else:
                                # For failed LLM analysis, evaluate original content with fallback criteria
                                # Allow original content based on configured minimum length
                                min_length = self.config.get("application", {}).get("min_content_length", 5)
                                if not content or len(content.strip()) < min_length:
                                    should_skip = True
                        if should_skip:
                            # Log detailed reasons for skipping
                            skip_reasons = []
                            if llm_success:
                                # Reasons for skipping when LLM was successful
                                if not response:
                                    skip_reasons.append("empty response")
                                if is_placeholder:
                                    skip_reasons.append("placeholder content detected")
                                if not has_sufficient_content and len(response.strip()) < 10:
                                    skip_reasons.append(f"insufficient content length ({len(response.strip())} chars)")
                            else:
                                # Reasons for skipping when LLM failed
                                min_length = self.config.get("application", {}).get("min_content_length", 5)
                                if not content or len(content.strip()) < min_length:
                                    skip_reasons.append(f"original content too short ({len(content.strip()) if content else 0} chars, min: {min_length})")
                            # Log content snippet for debugging
                            content_snippet = content[:50] + ('...' if len(content) > 50 else '')
                            section.log_step("Skipping Telegram posting for message - content")
                            section.log_step(f"Skip details - Message ID: {processed_message.get('id')}, Reason: {','.join(skip_reasons) if skip_reasons else 'low quality content'}, Content snippet: {content_snippet}")
                            continue
                        # Enhanced validation to prevent fake info from being posted
                        validation_errors = []
                        # Additional validation for trading signals
                        llm_success = llm_analysis and llm_analysis.get('success', False)
                        response = llm_analysis.get('response', '') if llm_analysis else ''
                        # Check for fake content before posting
                        # If LLM was successful, validate the LLM response, otherwise validate original content
                        content_to_validate = response if llm_success and response else content
                        fake_detection = detect_fake_content(content_to_validate, self.config)

                        # Get configurable threshold from config
                        fake_threshold = self.config.get("application", {}).get("validation_thresholds", {}).get("fake_content_confidence", 0.7)

                        if fake_detection["is_fake"] and fake_detection["confidence"] > fake_threshold:
                            validation_errors.append(f"Fake content detected: {', '.join(fake_detection['reasons'])}")

                        # Check for role-playing content before posting
                        roleplay_detection = detect_roleplay_content(content_to_validate, self.config)

                        # Get configurable threshold from config
                        roleplay_threshold = self.config.get("application", {}).get("validation_thresholds", {}).get("roleplay_confidence", 0.7)

                        if roleplay_detection["is_roleplay"] and roleplay_detection["confidence"] > roleplay_threshold:
                            validation_errors.append(f"Role-playing content detected: {', '.join(roleplay_detection['reasons'])}")
                        
                        # Log debug information if debug mode is enabled
                        debug_mode = self.config.get("application", {}).get("debug_mode", False)
                        if debug_mode:
                            section.log_step("Debug mode - Content validation results")
                            section.log_step(f"Fake detection - Is fake: {fake_detection['is_fake']}, Confidence: {fake_detection['confidence']}, Threshold: {fake_threshold}, Bypass enabled: {fake_detection.get('bypass_enabled', False)}")
                            section.log_step(f"Roleplay detection - Is roleplay: {roleplay_detection['is_roleplay']}, Confidence: {roleplay_detection['confidence']}, Threshold: {roleplay_threshold}, Speakers: {roleplay_detection.get('unique_speakers', 0)}")
                        if llm_success and response:
                            # Validate trading signal content
                            signal_validation = validate_trading_signal_content(response, self.config)
                            
                            # Get configurable threshold from config
                            signal_threshold = self.config.get("application", {}).get("validation_thresholds", {}).get("trading_signal_confidence", 0.7)
                            
                            if not signal_validation["is_valid"] and signal_validation["confidence"] > signal_threshold:
                                validation_errors.append(f"Invalid trading signal: {', '.join(signal_validation['reasons'])}")
                            
                            # Log debug information if debug mode is enabled
                            if debug_mode:
                                section.log_step("Debug mode - Trading signal validation results")
                                section.log_step(f"Debug details - Is valid: {signal_validation['is_valid']}, Confidence: {signal_validation['confidence']}, Threshold: {signal_threshold}, Bypass enabled: {signal_validation.get('bypass_enabled', False)}")
                        # Check if content is sufficiently detailed
                        # If LLM was successful, check the LLM response, otherwise check original content
                        content_to_check = response if llm_success and response else content
                        
                        # Get configurable min word count from config
                        min_word_count = self.config.get("application", {}).get("validation_thresholds", {}).get("min_word_count", 3)
                        
                        if not is_content_sufficiently_detailed(content_to_check, min_word_count, self.config):
                            validation_errors.append("Content is not sufficiently detailed")
                        
                        # Log debug information if debug mode is enabled
                        if debug_mode:
                            word_count = len(content_to_check.strip().split()) if content_to_check else 0
                            section.log_step("Debug mode - Content detail validation results")
                            section.log_step(f"Debug details - Word count: {word_count}, Min required: {min_word_count}, Bypass enabled: {self.config.get('application', {}).get('bypass_content_validation', False)}")
                        # Additional check for placeholder content
                        placeholder_indicators = [
                            'placeholder',
                            'XXX',
                            'Note: This is a placeholder',
                            'did not provide specific messages',
                            'generic summary',
                            'no specific information',
                            'no meaningful content'
                        ]
                        content_lower = content_to_check.lower() if content_to_check else ""
                        is_placeholder = any(indicator.lower() in content_lower for indicator in placeholder_indicators)
                        if is_placeholder:
                            validation_errors.append("Placeholder content detected")
                        # If any validation errors, skip posting (unless debug mode or bypass is enabled)
                        if validation_errors and not (debug_mode or bypass_validation):
                            section.log_step("Skipping Telegram posting for message - validation failed")
                            section.log_step(f"Validation details - Message ID: {processed_message.get('id')}, Validation errors: {validation_errors})")
                            continue
                        elif validation_errors and (debug_mode or bypass_validation):
                            section.log_step("Debug/bypass mode - proceeding despite validation errors")
                            section.log_step(f"Validation details - Message ID: {processed_message.get('id')}, Validation errors: {validation_errors}, Debug mode: {debug_mode}, Bypass enabled: {bypass_validation})")
                        # Check if message content is unique before posting (double check)
                        session_check = await self.db_service.get_session()
                        try:
                            # Use LLM response for duplicate check when available, otherwise use original content
                            content_for_duplicate_check = response if llm_success and response else content
                            # Determine the correct message type based on whether it's a reply
                            # Per user requirement: All messages should be stored as "telegram_post" in the database
                            # reply_telegram_post is only used when replying to user messages in tg_chat_id
                            message_type = "telegram_post"
                            is_unique = await is_message_unique(session_check, register.project_id, content_for_duplicate_check, message_type)
                            if not is_unique:
                                # Log content snippet for debugging
                                content_snippet = content_for_duplicate_check[:50] + ('...' if len(content_for_duplicate_check) > 50 else '')
                                with langchain_processor_logger.begin_section("Telegram Posting") as section:
                                    section.log_step("Skipping Telegram posting for message - content already posted")
                                    section.log_step(f"Content details - Message ID: {processed_message.get('id')}, Content snippet: {content_snippet})")
                                continue
                        finally:
                            await session_check.close()
                        # Additional check to ensure we're not posting empty or whitespace-only content
                        content_to_check = response if llm_success and response else content
                        if not content_to_check or not content_to_check.strip():
                            with langchain_processor_logger.begin_section("Telegram Posting") as section:
                                section.log_step("Skipping Telegram posting for message - content is empty or whitespace only")
                                section.log_step(f"Message details - Message ID: {processed_message.get('id')}")
                            continue
                        # Format the message based on LLM success status
                        message_to_send = ""
                        if llm_success and response:
                            # Check if this was a reply message with complete reply chain
                            complete_reply_chain = llm_analysis.get('complete_reply_chain', '') if llm_analysis else ''
                            if complete_reply_chain:
                                # For reply messages: original content + all reply content until latest + LLM analysis
                                message_to_send = f"{complete_reply_chain}\n\n{response}"
                            else:
                                # For non-reply messages: original content + LLM analysis
                                message_to_send = f"{content}\n\n{response}"
                        else:
                            # If LLM analysis failed, post the original content with a note
                            message_to_send = f"{content}\n[Note: LLM analysis failed, posting original content]"
                        # Add date information to the message
                        from datetime import datetime
                        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        # Check if we have date range information in the processed message
                        date_info = ""
                        date_range = processed_message.get('date_range')
                        if date_range:
                            start_date = date_range.get('start')
                            end_date = date_range.get('end')
                            if start_date and end_date:
                                date_info = f"[{start_date} to {end_date}] "
                            elif start_date:
                                date_info = f"[From {start_date}] "
                            elif end_date:
                                date_info = f"[Up to {end_date}] "
                        else:
                            processing_timestamp = processed_message.get('processing_timestamp')
                            if processing_timestamp:
                                # Use processing timestamp if available
                                date_info = f"[{processing_timestamp}] "
                            else:
                                # Use current date as fallback
                                date_info = f"[{current_date}] "
                        formatted_message = f"{date_info}{message_to_send}"
                        # Send the message to the configured chat using Telegram Bot API
                        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                        payload = {
                            "chat_id": chat_id,
                            "text": formatted_message
                        }
                        # Check if this is a reply to a user message and reply_mentions is enabled
                        # If reply_mentions is 1 and the message has a user_id that is not the bot itself,
                        # then we should reply to the user's message
                        if register.reply_mentions == 1:
                            user_id = processed_message.get('user_id', '')
                            # Check if this message has a user_id and it's not empty
                            # Also check that it's not the bot itself (bot messages typically don't have user_id or have bot-specific IDs)
                            if user_id and str(user_id).strip():
                                # This is a user message, and we should reply to it
                                # We need to get the actual message_id from Telegram to reply to it
                                # For now, we'll just log that we would reply to this message
                                with langchain_processor_logger.begin_section("Telegram Posting") as section:
                                    section.log_step("Reply mentions enabled for project. Would reply to user message from user")
                                    section.log_step(f"Project details - Project ID: {register.project_id}, User ID: {user_id}")
                                # In a full implementation, we would need to store the Telegram message_id 
                                # when we scrape messages and then use that to reply
                                # TODO: Implement actual reply functionality
                                # This would involve:
                                # 1. Storing the Telegram message_id when scraping messages
                                # 2. Using the Telegram Bot API to reply to the specific message
                                # 3. Creating a reply_telegram_post record in the messages table
                                # For now, we'll create a reply_telegram_post record to indicate that
                                # we should reply to this user message
                                message_type = "reply_telegram_post"
                        # Add message_thread_id if available (for forum topics)
                        if message_thread_id:
                            payload["message_thread_id"] = message_thread_id
                        # Also check if there's a specific thread ID for this message's entity
                        elif entities_with_thread_info and len(entities_with_thread_info) > 0:
                            # For now, use the first entity's thread ID if available
                            # In a more sophisticated implementation, we could match by entity
                            first_entity_thread_id = entities_with_thread_info[0].get('message_thread_id')
                            if first_entity_thread_id:
                                payload["message_thread_id"] = first_entity_thread_id
                        # Enhanced error handling with retry mechanism and exponential backoff
                        max_retries = 3
                        base_delay = 1  # Base delay in seconds
                        
                        for attempt in range(max_retries + 1):
                            try:
                                section.log_step(f"Telegram API request attempt {attempt + 1}/{max_retries + 1}")
                                section.log_step(f"Request details - URL: {url}, Chat ID: {chat_id}, Message length: {len(formatted_message)}")
                                
                                async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=30)) as http_response:
                                    # Enhanced logging of HTTP status codes and response bodies
                                    response_text = await http_response.text()
                                    section.log_step(f"Telegram API response - Status: {http_response.status}, Content-Type: {http_response.headers.get('Content-Type', 'unknown')}")
                                    
                                    if http_response.status == 200:
                                        section.log_step("Successfully posted message to Telegram")
                                        section.log_step(f"Response body: {response_text[:200]}{'...' if len(response_text) > 200 else ''}")

                                        # Track this message as successfully posted
                                        message_timestamp = processed_message.get('created_at', int(time.time()))
                                        successfully_posted_messages.append(processed_message)

                                        # Update max timestamp if this message is newer
                                        if max_posted_timestamp is None or message_timestamp > max_posted_timestamp:
                                            max_posted_timestamp = message_timestamp

                                        # Update the message type to indicate it's been posted
                                        db_session = await self.db_service.get_session()
                                        try:
                                            from sqlalchemy import select
                                            # Create a message record in the messages table to track what was posted
                                            # content should be the LLM's final output (response)
                                            # message should be the formatted Telegram link
                                            message_content = response if llm_success and response else content
                                            # Determine message type based on whether it's a reply
                                            # Per user requirement: All messages should be stored as "telegram_post" in the database
                                            # reply_telegram_post is only used when replying to user messages in tg_chat_id
                                            message_type = "telegram_post"
                                            # Determine what content to store in message_used
                                            # For reply messages, store the complete reply chain as concatenated string
                                            # For non-reply messages, use the original content
                                            message_used_content = content  # Default to original content

                                            # Check if this is a reply message
                                            if processed_message.get('reply_to'):
                                                complete_reply_chain = llm_analysis.get('complete_reply_chain', '') if llm_analysis else ''
                                                if complete_reply_chain:
                                                    # Store the complete reply chain as concatenated string
                                                    message_used_content = complete_reply_chain
                                            new_message = Message(
                                                project_id=register.project_id,
                                                message_id=processed_message.get('message_id', 0),
                                                entities_id=processed_message.get('entities_id', 0),
                                                type=message_type,  # Use the determined message type
                                                content=message_content,  # Store the LLM's final output or original content
                                                message=content,  # Store the original message content temporarily
                                                message_used=message_used_content,  # Store structured content for reply messages or original content
                                                reply_to=processed_message.get('reply_to'),  # Store reply information
                                                created_at=processed_message.get('created_at', int(time.time()))
                                            )
                                            db_session.add(new_message)
                                            await db_session.commit()
                                            # Format the message content after saving
                                            formatted_content = await format_message_link(db_session, new_message)
                                            new_message.message = formatted_content  # Only update the message field with the formatted link
                                            await db_session.commit()
                                            section.log_step(f"Created message record for tg_content with type {message_type}")
                                            section.log_step(f"Content details - Content ID: {processed_message.get('id', 'unknown')}")
                                        finally:
                                            await db_session.close()
                                        break  # Success, exit retry loop
                                    
                                    # Handle different types of Telegram API errors
                                    elif http_response.status == 400:
                                        section.log_step("Telegram API error - Bad Request (400)")
                                        section.log_step(f"Response body: {response_text}")
                                        # Parse error details if available
                                        try:
                                            import json
                                            error_data = json.loads(response_text)
                                            error_code = error_data.get('error_code', 'unknown')
                                            description = error_data.get('description', 'No description')
                                            section.log_step(f"Error details - Code: {error_code}, Description: {description}")
                                        except:
                                            section.log_step("Could not parse error response as JSON")
                                        break  # Don't retry on bad request errors
                                    
                                    elif http_response.status == 401:
                                        section.log_step("Telegram API error - Unauthorized (401) - Invalid bot token")
                                        section.log_step(f"Response body: {response_text}")
                                        section.log_step(f"Bot token (masked): {bot_token[:10]}...{bot_token[-5:] if len(bot_token) > 15 else 'too_short'}")
                                        break  # Don't retry on auth errors
                                    
                                    elif http_response.status == 403:
                                        section.log_step("Telegram API error - Forbidden (403) - Bot blocked or insufficient permissions")
                                        section.log_step(f"Response body: {response_text}")
                                        section.log_step(f"Chat ID: {chat_id}")
                                        break  # Don't retry on permission errors
                                    
                                    elif http_response.status == 429:
                                        # Rate limiting - extract retry-after header if available
                                        retry_after = http_response.headers.get('Retry-After', '60')
                                        section.log_step(f"Telegram API error - Rate Limited (429) - Retry after {retry_after} seconds")
                                        section.log_step(f"Response body: {response_text}")
                                        
                                        if attempt < max_retries:
                                            wait_time = int(retry_after) + (attempt * 5)  # Add extra delay for each retry
                                            section.log_step(f"Waiting {wait_time} seconds before retry due to rate limiting")
                                            await asyncio.sleep(wait_time)
                                            continue
                                        else:
                                            section.log_step("Max retries reached for rate limited request")
                                            break
                                    
                                    elif http_response.status >= 500:
                                        section.log_step(f"Telegram API error - Server Error ({http_response.status})")
                                        section.log_step(f"Response body: {response_text}")
                                        
                                        if attempt < max_retries:
                                            # Exponential backoff for server errors
                                            wait_time = base_delay * (2 ** attempt)
                                            section.log_step(f"Server error, retrying in {wait_time} seconds (attempt {attempt + 1}/{max_retries + 1})")
                                            await asyncio.sleep(wait_time)
                                            continue
                                        else:
                                            section.log_step("Max retries reached for server error")
                                            break
                                    
                                    else:
                                        section.log_step(f"Telegram API error - Unexpected status code ({http_response.status})")
                                        section.log_step(f"Response body: {response_text}")
                                        section.log_step(f"Response headers: {dict(http_response.headers)}")
                                        
                                        if attempt < max_retries:
                                            wait_time = base_delay * (2 ** attempt)
                                            section.log_step(f"Unexpected error, retrying in {wait_time} seconds (attempt {attempt + 1}/{max_retries + 1})")
                                            await asyncio.sleep(wait_time)
                                            continue
                                        else:
                                            section.log_step("Max retries reached for unexpected error")
                                            break
                            
                            except asyncio.TimeoutError:
                                section.log_step(f"Telegram API request timeout (attempt {attempt + 1}/{max_retries + 1})")
                                if attempt < max_retries:
                                    wait_time = base_delay * (2 ** attempt)
                                    section.log_step(f"Timeout error, retrying in {wait_time} seconds")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    section.log_step("Max retries reached for timeout error")
                                    break
                            
                            except aiohttp.ClientError as client_error:
                                section.log_step(f"Telegram API client error (attempt {attempt + 1}/{max_retries + 1})")
                                section.log_step(f"Client error details: {str(client_error)}")
                                if attempt < max_retries:
                                    wait_time = base_delay * (2 ** attempt)
                                    section.log_step(f"Client error, retrying in {wait_time} seconds")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    section.log_step("Max retries reached for client error")
                                    break
                            
                            except Exception as http_error:
                                section.log_step(f"Unexpected HTTP error (attempt {attempt + 1}/{max_retries + 1})")
                                section.log_step(f"Error details: {str(http_error)}")
                                section.log_step(f"Error type: {type(http_error).__name__}")
                                if attempt < max_retries:
                                    wait_time = base_delay * (2 ** attempt)
                                    section.log_step(f"Unexpected HTTP error, retrying in {wait_time} seconds")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    section.log_step("Max retries reached for unexpected HTTP error")
                                    break
                        # Add a small delay to avoid rate limiting
                        await asyncio.sleep(2)  # Increased delay to 2 seconds

                    # Update the last post time with the maximum timestamp from successfully posted messages
                    if max_posted_timestamp is not None and successfully_posted_messages:
                        db_session = await self.db_service.get_session()
                        try:
                            from helpers.bot_state_helpers import update_last_post_time
                            await update_last_post_time(db_session, register.project_id, max_posted_timestamp)
                            await db_session.commit()

                            # Also update the in-memory timestamp to keep it synchronized
                            previous_in_memory_timestamp = self.last_processed_timestamps.get(register.project_id, None)
                            self.last_processed_timestamps[register.project_id] = max_posted_timestamp
                            section.log_step("Updated last post time with maximum timestamp from posted messages")
                            section.log_step(f"Timestamp details - Project ID: {register.project_id}, Previous timestamp: {previous_in_memory_timestamp}, New timestamp: {max_posted_timestamp}")
                            section.log_step(f"Successfully posted messages count: {len(successfully_posted_messages)}")
                        finally:
                            await db_session.close()
            except Exception as e:
                with langchain_processor_logger.begin_section("Telegram Posting") as section:
                    section.log_step("Error sending messages to Telegram for project")
                    section.log_step(f"Error details - Project ID: {register.project_id}, Error: {str(e)}")
        except Exception as e:
            with langchain_processor_logger.begin_section("Telegram Posting") as section:
                section.log_step("Error posting to Telegram for register")
                section.log_step(f"Error details - Project ID: {register.project_id}, Error: {str(e)}")

    async def _process_pending_replies(self, register: Register, entity_ids: List[int]) -> None:
        """
        Process pending reply messages that may now have their parent messages available.

        Args:
            register: The register being processed
            entity_ids: List of entity IDs for this register
        """
        try:
            with langchain_processor_logger.begin_section("Reply Queue Processing") as section:
                section.log_step("Checking for pending reply messages")
                section.log_step(f"Project ID: {register.project_id}, Entity IDs: {entity_ids}")

                session = await self.db_service.get_session()
                try:
                    processable_replies = []

                    # Check each entity for pending replies
                    for entity_id in entity_ids:
                        entity_replies = await reply_queue_manager.retry_pending_replies(session, entity_id)
                        processable_replies.extend(entity_replies)

                    if processable_replies:
                        section.log_step(f"Found processable reply messages: {len(processable_replies)}")

                        # Process each reply message
                        processed_replies = []
                        for reply_message in processable_replies:
                            try:
                                # Process the reply message with LLM (now that parent is available)
                                llm_result = await self._process_message_with_llm(register, reply_message.content, reply_message)
                                llm_success = llm_result and llm_result.get('success', False)

                                if llm_success:
                                    # Get entity name for enhanced posting
                                    entity_name = f"Entity_{reply_message.entities_id}"  # Default fallback

                                    processed_message = {
                                        'content': reply_message.content,
                                        'llm_analysis': llm_result,
                                        'entities_id': reply_message.entities_id,
                                        'entity_id': reply_message.entities_id,
                                        'entity_name': entity_name,
                                        'message_id': reply_message.message_id,
                                        'user_id': reply_message.user_id,
                                        'created_at': reply_message.created_at,
                                        'reply_to': reply_message.reply_to,
                                        'processed_message': {
                                            'entity_id': reply_message.entities_id,
                                            'message_id': reply_message.message_id,
                                            'created_at': reply_message.created_at,
                                            'reply_to': reply_message.reply_to
                                        }
                                    }
                                    processed_replies.append(processed_message)

                                    # Remove from reply queue
                                    reply_queue_manager.remove_processed_reply(reply_message.entities_id, reply_message.message_id)

                                    section.log_step("Successfully processed pending reply")
                                    section.log_step(f"Reply details - Message ID: {reply_message.message_id}, Parent ID: {reply_message.reply_to}")

                            except Exception as e:
                                section.log_step("Error processing pending reply")
                                section.log_step(f"Reply details - Message ID: {reply_message.message_id}, Error: {str(e)}")

                        # Post processed replies to Telegram if any were successful
                        if processed_replies:
                            section.log_step(f"Posting processed reply messages: {len(processed_replies)}")
                            await self._post_to_telegram_enhanced(register, processed_replies)
                    else:
                        section.log_step("No pending reply messages ready for processing")

                    # Log queue statistics
                    queue_stats = reply_queue_manager.get_queue_stats()
                    section.log_step("Reply queue statistics")
                    section.log_step(f"Queue stats - Total pending: {queue_stats['total_pending_replies']}, Entities with pending: {queue_stats['entities_with_pending']}")

                finally:
                    await session.close()

        except Exception as e:
            with langchain_processor_logger.begin_section("Reply Queue Processing") as section:
                section.log_step("Error processing pending replies")
                section.log_step(f"Error details - Project ID: {register.project_id}, Error: {str(e)}")
