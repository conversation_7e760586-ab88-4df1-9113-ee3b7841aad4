"""
Test suite for the Master Content Workflow integration.
Tests the unified entry point that automatically routes content to appropriate pipelines.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from helpers.langgraph.core import MasterContentWorkflow
from helpers.langgraph.factory import create_master_content_workflow
from services.langgraph_service import LanggraphService


class TestMasterContentWorkflow:
    """Test cases for the Master Content Workflow."""
    
    @pytest.fixture
    def sample_crypto_messages(self):
        """Sample crypto-related messages for testing."""
        return [
            {"content": "Bitcoin is pumping! BTC to the moon! Buy signal at $45000"},
            {"content": "Ethereum analysis: ETH showing bullish patterns, target $3000"},
            {"content": "Trading signal: Long SOL at current price, stop loss at $180"}
        ]
    
    @pytest.fixture
    def sample_normal_messages(self):
        """Sample normal messages for testing."""
        return [
            {"content": "Great weather today! Perfect for a walk in the park."},
            {"content": "Just finished reading an amazing book about history."},
            {"content": "Looking forward to the weekend plans with family."}
        ]
    
    @pytest.fixture
    def sample_mixed_messages(self):
        """Sample mixed content messages for testing."""
        return [
            {"content": "Bitcoin is interesting but I also love cooking pasta"},
            {"content": "The weather is nice today"},
            {"content": "ETH price analysis shows potential growth"},
            {"content": "My favorite movie is about space exploration"}
        ]
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return {
            'master_workflow': {
                'enable_unified_processing': True,
                'crypto_detection_threshold': 0.6,
                'crypto_ratio_threshold': 0.8,
                'mixed_content_threshold': 0.2,
                'default_pipeline': 'master',
                'fallback_pipeline': 'normal',
                'enable_pipeline_logging': True,
                'routing_preferences': {
                    'prefer_crypto_pipeline': False,
                    'enable_mixed_processing': True,
                    'strict_crypto_classification': False
                }
            },
            'application': {
                'validation_thresholds': {
                    'fake_content_confidence': 0.7,
                    'trading_signal_confidence': 0.7,
                    'roleplay_confidence': 0.7
                }
            }
        }
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    def test_master_workflow_initialization(self, mock_config_loader, mock_config):
        """Test that master workflow initializes correctly with configuration."""
        mock_config_loader.return_value = mock_config
        
        workflow = MasterContentWorkflow("test_master_workflow")
        
        assert workflow.name == "test_master_workflow"
        assert workflow.crypto_detection_threshold == 0.6
        assert workflow.crypto_ratio_threshold == 0.8
        assert workflow.mixed_content_threshold == 0.2
        assert workflow.routing_preferences['enable_mixed_processing'] is True
    
    def test_crypto_confidence_calculation(self):
        """Test crypto confidence scoring algorithm."""
        workflow = MasterContentWorkflow()
        
        # High confidence crypto content
        high_crypto = "Bitcoin BTC trading signal buy now target $50000 stop loss bullish"
        confidence = workflow._calculate_crypto_confidence(high_crypto)
        assert confidence > 0.8
        
        # Medium confidence crypto content
        medium_crypto = "Market analysis shows potential growth in blockchain sector"
        confidence = workflow._calculate_crypto_confidence(medium_crypto)
        assert 0.3 <= confidence <= 0.7
        
        # Low confidence normal content
        normal_content = "Beautiful sunset today, perfect weather for hiking"
        confidence = workflow._calculate_crypto_confidence(normal_content)
        assert confidence < 0.3
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    @patch('helpers.langgraph.content_validator.LanggraphContentValidator')
    async def test_crypto_content_classification(self, mock_validator, mock_config_loader, 
                                               mock_config, sample_crypto_messages):
        """Test that crypto content is correctly classified."""
        mock_config_loader.return_value = mock_config
        mock_validator_instance = Mock()
        mock_validator_instance._is_crypto_related.return_value = True
        mock_validator.return_value = mock_validator_instance
        
        workflow = MasterContentWorkflow("test_crypto_classification")
        
        # Create mock state
        state = Mock()
        state.messages = sample_crypto_messages
        state.context = {}
        
        result = await workflow._classify_content(state)
        
        assert result['content_type'] == 'crypto'
        assert result['crypto_ratio'] > 0.8
        assert result['crypto_confidence'] > 0.6
        assert result['next_action'] == 'route_content'
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    @patch('helpers.langgraph.content_validator.LanggraphContentValidator')
    async def test_normal_content_classification(self, mock_validator, mock_config_loader,
                                               mock_config, sample_normal_messages):
        """Test that normal content is correctly classified."""
        mock_config_loader.return_value = mock_config
        mock_validator_instance = Mock()
        mock_validator_instance._is_crypto_related.return_value = False
        mock_validator.return_value = mock_validator_instance
        
        workflow = MasterContentWorkflow("test_normal_classification")
        
        # Create mock state
        state = Mock()
        state.messages = sample_normal_messages
        state.context = {}
        
        result = await workflow._classify_content(state)
        
        assert result['content_type'] == 'normal'
        assert result['crypto_ratio'] < 0.3
        assert result['crypto_confidence'] < 0.4
        assert result['next_action'] == 'route_content'
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    @patch('helpers.langgraph.content_validator.LanggraphContentValidator')
    async def test_mixed_content_classification(self, mock_validator, mock_config_loader,
                                              mock_config, sample_mixed_messages):
        """Test that mixed content is correctly classified."""
        mock_config_loader.return_value = mock_config
        mock_validator_instance = Mock()
        # Mock mixed responses - some crypto, some normal
        mock_validator_instance._is_crypto_related.side_effect = [False, False, True, False]
        mock_validator.return_value = mock_validator_instance
        
        workflow = MasterContentWorkflow("test_mixed_classification")
        
        # Create mock state
        state = Mock()
        state.messages = sample_mixed_messages
        state.context = {}
        
        result = await workflow._classify_content(state)
        
        assert result['content_type'] == 'mixed'
        assert 0.2 < result['crypto_ratio'] < 0.8
        assert result['next_action'] == 'route_content'
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    async def test_content_routing_decisions(self, mock_config_loader, mock_config):
        """Test that content routing makes correct decisions."""
        mock_config_loader.return_value = mock_config
        workflow = MasterContentWorkflow("test_routing")
        
        # Test crypto routing
        crypto_state = Mock()
        crypto_state.get.side_effect = lambda key, default=None: {
            'content_type': 'crypto',
            'crypto_ratio': 0.9,
            'crypto_confidence': 0.8
        }.get(key, default)
        
        result = await workflow._route_content(crypto_state)
        assert result['routing_decision'] == 'crypto'
        assert result['next_action'] == 'process_crypto'
        
        # Test normal routing
        normal_state = Mock()
        normal_state.get.side_effect = lambda key, default=None: {
            'content_type': 'normal',
            'crypto_ratio': 0.1,
            'crypto_confidence': 0.2
        }.get(key, default)
        
        result = await workflow._route_content(normal_state)
        assert result['routing_decision'] == 'normal'
        assert result['next_action'] == 'process_normal'
        
        # Test mixed routing
        mixed_state = Mock()
        mixed_state.get.side_effect = lambda key, default=None: {
            'content_type': 'mixed',
            'crypto_ratio': 0.5,
            'crypto_confidence': 0.5
        }.get(key, default)
        
        result = await workflow._route_content(mixed_state)
        assert result['routing_decision'] == 'mixed'
        assert result['next_action'] == 'process_mixed'


class TestLanggraphServiceIntegration:
    """Test integration with LanggraphService."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for service testing."""
        return {
            'master_workflow': {
                'enable_unified_processing': True,
                'default_pipeline': 'master'
            },
            'application': {
                'validation_thresholds': {
                    'fake_content_confidence': 0.7
                }
            }
        }
    
    @patch('utils.config_loader.ConfigLoader.load_config')
    @patch('helpers.langgraph.content_validator.LanggraphContentValidator')
    async def test_unified_message_processing(self, mock_validator, mock_config_loader, mock_config):
        """Test unified message processing through LanggraphService."""
        mock_config_loader.return_value = mock_config
        
        # Mock content validator
        mock_validator_instance = Mock()
        mock_validator_instance.validate_message_batch.return_value = {
            'validated_messages': [{"content": "Bitcoin analysis shows bullish trend"}],
            'original_count': 1,
            'validated_count': 1,
            'filtered_count': 0
        }
        mock_validator.return_value = mock_validator_instance
        
        service = LanggraphService()
        
        # Mock the workflow execution
        with patch.object(service, 'create_and_run_workflow') as mock_workflow:
            mock_workflow.return_value = {
                'result': {
                    'harmonized_output': {
                        'success': True,
                        'processing_pipeline': 'crypto',
                        'content_type': 'crypto',
                        'generated_content': 'Crypto analysis completed'
                    }
                }
            }
            
            messages = [{"content": "Bitcoin analysis shows bullish trend"}]
            result = await service.process_messages_unified(messages)
            
            assert result['success'] is True
            assert result['processing_pipeline'] == 'crypto'
            assert result['content_type'] == 'crypto'
            assert 'validation_result' in result
            
            # Verify workflow was called with correct parameters
            mock_workflow.assert_called_once_with(
                workflow_type="master",
                name="unified_message_processing",
                input_state={
                    "messages": [{"content": "Bitcoin analysis shows bullish trend"}],
                    "context": {}
                }
            )


class TestWorkflowFactory:
    """Test workflow factory integration."""
    
    def test_create_master_content_workflow(self):
        """Test factory creates master workflow correctly."""
        workflow = create_master_content_workflow("test_factory_workflow")
        
        assert isinstance(workflow, MasterContentWorkflow)
        assert workflow.name == "test_factory_workflow"
    
    @patch('helpers.langgraph.factory.create_master_content_workflow')
    def test_factory_workflow_type_master(self, mock_create_master):
        """Test that factory creates master workflow for 'master' type."""
        from helpers.langgraph.factory import create_workflow
        
        mock_workflow = Mock()
        mock_create_master.return_value = mock_workflow
        
        result = create_workflow("master", "test_master")
        
        mock_create_master.assert_called_once_with("test_master")
        assert result == mock_workflow


if __name__ == "__main__":
    pytest.main([__file__])
